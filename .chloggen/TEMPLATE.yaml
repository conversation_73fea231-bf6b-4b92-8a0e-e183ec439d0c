# One of 'breaking', 'deprecation', 'new_component', 'enhancement', 'bug_fix'
change_type:

# The name of the component, or a single word describing the area of concern, (e.g. collector, target allocator, auto-instrumentation, opamp, github action)
component:

# A brief description of the change. Surround your text with quotes ("") if it needs to start with a backtick (`).
note:

# One or more tracking issues related to the change
issues: []

# (Optional) One or more lines of additional information to render under the primary note.
# These lines will be padded with 2 spaces and then inserted directly into the document.
# Use pipe (|) for multiline entries.
subtext:
