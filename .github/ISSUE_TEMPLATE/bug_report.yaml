name: Bug report
description: Create a report to help us improve
labels: ["bug", "needs triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! Please make sure to fill out the entire form below, providing as much context as you can in order to help us triage and track down your bug as quickly as possible.

        Before filing a bug, please be sure you have searched through [existing bugs](https://github.com/open-telemetry/opentelemetry-operator/issues?q=is%3Aissue+is%3Aopen+sort%3Aupdated-desc+label%3Abug) to see if an existing issue covers your bug.
  - type: dropdown
    id: component
    attributes:
      label: Component(s)
      description: Which component(s) does your bug report concern?
      multiple: true
      options:
      - collector
      - auto-instrumentation
      - target allocator
      - opamp
  - type: textarea
    attributes:
      label: What happened?
      description: Please provide as much detail as you reasonably can.
      value: |
        ## Description

        ## Steps to Reproduce

        ## Expected Result

        ## Actual Result

    validations:
      required: true
  - type: input
    attributes:
      label: Kubernetes Version
      description: What version did you use? (e.g., `1.23.0`)
    validations:
      required: true
  - type: input
    attributes:
      label: Operator version
      description: What version did you use? (e.g., `v0.4.0`, `1eb551b`, etc)
    validations:
      required: true
  - type: input
    attributes:
      label: Collector version
      description: What version did you use? (e.g., `v0.4.0`, `1eb551b`, etc)
    validations:
      required: true
  - type: textarea
    attributes:
      label: Environment information
      description: Please provide any additional information about your installation.
      value: |
        ## Environment
        OS: (e.g., "Ubuntu 20.04")
        Compiler(if manually compiled): (e.g., "go 14.2")
  - type: textarea
    attributes:
      label: Log output
      description: |
        Please copy and paste any relevant log output.
      render: shell
  - type: textarea
    attributes:
      label: Additional context
      description: Any additional information you think may be relevant to this issue.
