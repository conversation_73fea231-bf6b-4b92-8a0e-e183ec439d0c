version: 2
updates:
  - package-ecosystem: gomod
    directory: /
    schedule:
      interval: weekly
    # Create a group of dependencies to be updated together in one pull request
    groups:
      golang-org-x:
        patterns:
          - "golang.org/x/*"
      prometheus:
        patterns:
          - "github.com/prometheus-operator/prometheus-operator"
          - "github.com/prometheus-operator/prometheus-operator/*"
          - "github.com/prometheus/prometheus"
          - "github.com/prometheus/prometheus/*"
          - "github.com/prometheus/client_go"
          - "github.com/prometheus/client_go/*"
      kubernetes:
        patterns:
          - "k8s.io/*"
          - "sigs.k8s.io/*"
      otel:
        patterns:
          - "go.opentelemetry.io/otel"
          - "go.opentelemetry.io/otel/*"

  - package-ecosystem: github-actions
    directory: /
    schedule:
      interval: weekly
    groups:
      gha-docker:
        patterns:
          - "docker/*"

  - package-ecosystem: docker
    directory: /
    schedule:
      interval: weekly
    ignore:
      # Ignore minor version updates for kind node images
      - dependency-name: "kindest/node*"
        update-types: [ "version-update:semver-minor" ]

  - package-ecosystem: docker
    directory: /cmd/otel-allocator
    schedule:
      interval: weekly

  - package-ecosystem: docker
    directory: /cmd/operator-opamp-bridge
    schedule:
      interval: weekly
