# This action requires that any PR targeting the main branch should add a
# yaml file to the ./.chloggen/ directory. If a CHANGELOG entry is not required,
# or if performing maintance on the Changelog, add either \"[chore]\" to the title of
# the pull request or add the \"Skip Changelog\" label to disable this action.

name: changelog

on:
  pull_request:
    types: [opened, synchronize, reopened, labeled, unlabeled]
    branches:
      - main

env:
  # See: https://github.com/actions/cache/issues/810#issuecomment-1222550359
  # Cache downloads for this workflow consistently run in under 1 minute
  SEGMENT_DOWNLOAD_TIMEOUT_MINS: 5

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  changelog:
    runs-on: ubuntu-latest
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'dependencies') && !contains(github.event.pull_request.labels.*.name, 'Skip Changelog') && !contains(github.event.pull_request.title, '[chore]')}}

    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: "~1.24.4"

      - name: Ensure no changes to the CHANGELOG
        run: |
          if [[ $(git diff --name-only $(git merge-base origin/main ${{ github.event.pull_request.head.sha }}) ${{ github.event.pull_request.head.sha }} ./CHANGELOG.md) ]]
          then
            echo "The CHANGELOG should not be directly modified."
            echo "Please add a .yaml file to the ./.chloggen/ directory."
            echo "See CONTRIBUTING.md for more details."
            echo "Alternately, add either \"[chore]\" to the title of the pull request or add the \"Skip Changelog\" label if this job should be skipped."
            false
          else
            echo "The CHANGELOG was not modified."
          fi

      - name: Ensure ./.chloggen/*.yaml addition(s)
        run: |
          if [[ 1 -gt $(git diff --diff-filter=A --name-only $(git merge-base origin/main ${{ github.event.pull_request.head.sha }}) ${{ github.event.pull_request.head.sha }} ./.chloggen | grep -c \\.yaml) ]]
          then
            echo "No changelog entry was added to the ./.chloggen/ directory."
            echo "Please add a .yaml file to the ./.chloggen/ directory."
            echo "See CONTRIBUTING.md for more details."
            echo "Alternately, add either \"[chore]\" to the title of the pull request or add the \"Skip Changelog\" label if this job should be skipped."
            false
          else
            echo "A changelog entry was added to the ./.chloggen/ directory."
          fi

      - name: Validate ./.chloggen/*.yaml changes
        run: |
          make chlog-validate \
            || { echo "New ./.chloggen/*.yaml file failed validation."; exit 1; }
