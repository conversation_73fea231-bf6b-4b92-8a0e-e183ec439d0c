name: "Publish release on operator hub"
on:
  release:
    types: [published]

permissions:
  contents: read

jobs:
  operator-hub-prod-release:
    permissions: # required by the reusable workflow
      contents: write
    uses: ./.github/workflows/reusable-operator-hub-release.yaml
    with:
      org: redhat-openshift-ecosystem
      repo: community-operators-prod
      folder: openshift
    secrets:
      OPENTELEMETRYBOT_GITHUB_TOKEN: ${{ secrets.OPENTELEMETRYBOT_GITHUB_TOKEN }}

  operator-hub-community-release:
    permissions: # required by the reusable workflow
      contents: write
    uses: ./.github/workflows/reusable-operator-hub-release.yaml
    with:
      org: k8s-operatorhub
      repo: community-operators
      folder: community
    secrets:
      OPENTELEMETRYBOT_GITHUB_TOKEN: ${{ secrets.OPENTELEMETRYBOT_GITHUB_TOKEN }}
