name: "Publish target allocator"

on:
  push:
    paths:
      - "cmd/otel-allocator/**"
      - ".github/workflows/publish-target-allocator.yaml"
    branches:
      - main
    tags:
      - "v*"
  workflow_dispatch:

env:
  PLATFORMS: linux/amd64,linux/arm64,linux/s390x,linux/ppc64le

permissions:
  contents: read

jobs:
  publish:
    permissions:
      packages: write
      attestations: write
      id-token: write
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: "~1.24.4"

      # TODO: We're currently not using this. Should we?
      - name: Read version
        run: |
          echo "VERSION=$(git describe --tags | sed 's/^v//')" >> $GITHUB_ENV
          echo "VERSION_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" >> $GITHUB_ENV

      - name: Build the binary for each supported architecture
        run: |
          for platform in $(echo $PLATFORMS | tr "," "\n"); do
            arch=${platform#*/}
            echo "Building target allocator for $arch"
            make targetallocator ARCH=$arch
          done

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@902fa8ec7d6ecbf8d84d538b9b233a880e428804 # v5.7.0
        with:
          images: |
            otel/target-allocator
            ghcr.io/open-telemetry/opentelemetry-operator/target-allocator
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{raw}}
            type=ref,event=branch

      - name: Set up QEMU
        uses: docker/setup-qemu-action@29109295f81e9208d7d86ff1c6c12d2833863392 # v3.6.0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1

      - name: Cache Docker layers
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Log into Docker.io
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        if: ${{ github.event_name == 'push' }}
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Login to GitHub Package Registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        if: ${{ github.event_name == 'push' }}
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          context: cmd/otel-allocator
          platforms: ${{ env.PLATFORMS }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache
