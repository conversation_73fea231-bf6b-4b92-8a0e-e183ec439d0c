name: "Create the release"
on:
  push:
    branches:
      - 'release/**'
      - 'main'
    paths:
      - 'versions.txt'

permissions:
  contents: read

jobs:
  get-versions:
    runs-on: ubuntu-latest
    outputs:
      current_version: ${{ steps.get-versions.outputs.current_version }}
      desired_version: ${{ steps.get-versions.outputs.desired_version }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: "Get versions from versions.txt"
        id: get-versions
        run: |
          echo "desired_version=$(grep -v '#' versions.txt | grep operator= | awk -F= '{print $2}')" >> $GITHUB_OUTPUT
          echo "current_version=$(git describe --tags --abbrev=0 | sed 's/^v//')" >> $GITHUB_OUTPUT

  release:
    runs-on: ubuntu-latest
    needs: get-versions
    if: needs.get-versions.outputs.desired_version != needs.get-versions.outputs.current_version
    env:
      CURRENT_VERSION: ${{ needs.get-versions.outputs.current_version }}
      DESIRED_VERSION: ${{ needs.get-versions.outputs.desired_version }}
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    - name: Set up Go
      uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
      with:
        go-version: "~1.24.4"

    - name: "generate release resources"
      run: make release-artifacts IMG_PREFIX="ghcr.io/open-telemetry/opentelemetry-operator" VERSION=${DESIRED_VERSION}

    - name: "create the release in GitHub"
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: ./.ci/create-release-github.sh

    - name: "refresh go proxy module info on release"
      run: |
        curl https://proxy.golang.org/github.com/open-telemetry/opentelemetry-operator/@v/${DESIRED_VERSION}.info
