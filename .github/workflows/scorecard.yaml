name: "Scorecard tests"

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  scorecard-tests:
    name: test on k8s
    runs-on: ubuntu-latest
    strategy:
      matrix:
        kube-version:
          - "1.25"
          - "1.33"

    steps:

      - name: Set up Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        id: setup-go
        with:
          go-version: "~1.24.4"

      - name: Check out code into the Go module directory
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Cache tools
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: bin
          key: tools-${{ runner.os }}-${{ runner.arch }}-${{ hashFiles('Makefile') }}-${{ steps.setup-go.outputs.go-version }}

      - name: Install tools
        run: make install-tools

      - name: "start kind"
        env:
          KUBE_VERSION: ${{ matrix.kube-version }}
        run: make start-kind

      - name: "wait until cluster is ready"
        run:  kubectl wait --timeout=5m --for=condition=available deployment/coredns -n kube-system

      - name: "run scorecard test"
        run: make scorecard-tests

  scorecard-tests-check:
    runs-on: ubuntu-latest
    if: always()
    needs: [scorecard-tests]
    steps:
      - name: Print result
        run: echo ${{ needs.scorecard-tests.result }}
      - name: Interpret result
        run: |
          if [[ success == ${{ needs.scorecard-tests.result }} ]]
          then
            echo "All matrix jobs passed!"
          else
            echo "One or more matrix jobs failed."
            false
          fi
