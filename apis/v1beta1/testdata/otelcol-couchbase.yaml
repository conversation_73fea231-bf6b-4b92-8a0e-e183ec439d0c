receivers:
  prometheus/couchbase:
    config:
      scrape_configs:
        - job_name: 'couchbase'
          scrape_interval: 5s
          static_configs:
            - targets: ['couchbase:8091']
          basic_auth:
            username: 'otelu'
            password: 'otelpassword'
          metric_relabel_configs:
            # Include only a few key metrics
            - source_labels: [ __name__ ]
              regex: "(kv_ops)|\
                (kv_vb_curr_items)|\
                (kv_num_vbuckets)|\
                (kv_ep_cursor_memory_freed_bytes)|\
                (kv_total_memory_used_bytes)|\
                (kv_ep_num_value_ejects)|\
                (kv_ep_mem_high_wat)|\
                (kv_ep_mem_low_wat)|\
                (kv_ep_tmp_oom_errors)|\
                (kv_ep_oom_errors)"
              action: keep

processors:
  filter/couchbase:
    # Filter out prometheus scraping meta-metrics.
    metrics:
      exclude:
        match_type: strict
        metric_names:
          - scrape_samples_post_metric_relabeling
          - scrape_series_added
          - scrape_duration_seconds
          - scrape_samples_scraped
          - up

  metricstransform/couchbase:
    transforms:
      # Rename from prometheus metric name to OTel metric name.
      # We cannot do this with metric_relabel_configs, as the prometheus receiver does not
      # allow metric renames at this time.
      - include: kv_ops
        match_type: strict
        action: update
        new_name: "couchbase.bucket.operation.count"
      - include: kv_vb_curr_items
        match_type: strict
        action: update
        new_name: "couchbase.bucket.item.count"
      - include: kv_num_vbuckets
        match_type: strict
        action: update
        new_name: "couchbase.bucket.vbucket.count"
      - include: kv_ep_cursor_memory_freed_bytes
        match_type: strict
        action: update
        new_name: "couchbase.bucket.memory.usage.free"
      - include: kv_total_memory_used_bytes
        match_type: strict
        action: update
        new_name: "couchbase.bucket.memory.usage.used"
      - include: kv_ep_num_value_ejects
        match_type: strict
        action: update
        new_name: "couchbase.bucket.item.ejection.count"
      - include: kv_ep_mem_high_wat
        match_type: strict
        action: update
        new_name: "couchbase.bucket.memory.high_water_mark.limit"
      - include: kv_ep_mem_low_wat
        match_type: strict
        action: update
        new_name: "couchbase.bucket.memory.low_water_mark.limit"
      - include: kv_ep_tmp_oom_errors
        match_type: strict
        action: update
        new_name: "couchbase.bucket.error.oom.count.recoverable"
      - include: kv_ep_oom_errors
        match_type: strict
        action: update
        new_name: "couchbase.bucket.error.oom.count.unrecoverable"
      # Combine couchbase.bucket.error.oom.count.x and couchbase.bucket.memory.usage.x
      # metrics.
      - include: '^couchbase\.bucket\.error\.oom\.count\.(?P<error_type>unrecoverable|recoverable)$$'
        match_type: regexp
        action: combine
        new_name: "couchbase.bucket.error.oom.count"
      - include: '^couchbase\.bucket\.memory\.usage\.(?P<state>free|used)$$'
        match_type: regexp
        action: combine
        new_name: "couchbase.bucket.memory.usage"
      # Aggregate "result" label on operation count to keep label sets consistent across the metric datapoints
      - include: 'couchbase.bucket.operation.count'
        match_type: strict
        action: update
        operations:
          - action: aggregate_labels
            label_set: ["bucket", "op"]
            aggregation_type: sum

  transform/couchbase:
    metric_statements:
      - context: datapoint
        statements:
          - convert_gauge_to_sum("cumulative", true) where metric.name == "couchbase.bucket.operation.count"
          - set(metric.description, "Number of operations on the bucket.") where metric.name == "couchbase.bucket.operation.count"
          - set(metric.unit, "{operations}") where metric.name == "couchbase.bucket.operation.count"

          - convert_gauge_to_sum("cumulative", false) where metric.name == "couchbase.bucket.item.count"
          - set(metric.description, "Number of items that belong to the bucket.") where metric.name == "couchbase.bucket.item.count"
          - set(metric.unit, "{items}") where metric.name == "couchbase.bucket.item.count"

          - convert_gauge_to_sum("cumulative", false) where metric.name == "couchbase.bucket.vbucket.count"
          - set(metric.description, "Number of non-resident vBuckets.") where metric.name == "couchbase.bucket.vbucket.count"
          - set(metric.unit, "{vbuckets}") where metric.name == "couchbase.bucket.vbucket.count"

          - convert_gauge_to_sum("cumulative", false) where metric.name == "couchbase.bucket.memory.usage"
          - set(metric.description, "Usage of total memory available to the bucket.") where metric.name == "couchbase.bucket.memory.usage"
          - set(metric.unit, "By") where metric.name == "couchbase.bucket.memory.usage"

          - convert_gauge_to_sum("cumulative", true) where metric.name == "couchbase.bucket.item.ejection.count"
          - set(metric.description, "Number of item value ejections from memory to disk.") where metric.name == "couchbase.bucket.item.ejection.count"
          - set(metric.unit, "{ejections}") where metric.name == "couchbase.bucket.item.ejection.count"

          - convert_gauge_to_sum("cumulative", true) where metric.name == "couchbase.bucket.error.oom.count"
          - set(metric.description, "Number of out of memory errors.") where metric.name == "couchbase.bucket.error.oom.count"
          - set(metric.unit, "{errors}") where metric.name == "couchbase.bucket.error.oom.count"

          - set(metric.description, "The memory usage at which items will be ejected.") where metric.name == "couchbase.bucket.memory.high_water_mark.limit"
          - set(metric.unit, "By") where metric.name == "couchbase.bucket.memory.high_water_mark.limit"

          - set(metric.description, "The memory usage at which ejections will stop that were previously triggered by a high water mark breach.") where metric.name == "couchbase.bucket.memory.low_water_mark.limit"
          - set(metric.unit, "By") where metric.name == "couchbase.bucket.memory.low_water_mark.limit"

exporters:
  prometheus:
    endpoint: "0.0.0.0:9123"

service:
  pipelines:
    metrics/couchbase:
      receivers: [prometheus/couchbase]
      processors: [filter/couchbase, metricstransform/couchbase, transform/couchbase]
      exporters: [prometheus]
