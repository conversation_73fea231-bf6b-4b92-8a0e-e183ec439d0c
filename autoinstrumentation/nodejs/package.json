{"name": "@opentelemetry/k8s-autoinstrumentation", "version": "0.0.1", "private": true, "scripts": {"clean": "rimraf build/*", "postinstall": "copyfiles -f 'build/src/**' build/workspace/ && copyfiles 'node_modules/**' package.json build/workspace/ && npm -C build/workspace prune --omit=dev --no-package-lock"}, "devDependencies": {"copyfiles": "^2.4.1", "rimraf": "^6.0.1", "typescript": "^5.6.3"}, "dependencies": {"@opentelemetry/api": "1.9.0", "@opentelemetry/auto-instrumentations-node": "0.60.1"}}