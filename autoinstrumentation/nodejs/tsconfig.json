{
  "compilerOptions": {
    "rootDir": ".",
    "outDir": "build",

    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
    "composite": true,
    "declaration": true,
    "declarationMap": true,
    "forceConsistentCasingInFileNames": true,
    "incremental": true,
    "inlineSources": true,
    "module": "commonjs",
    "newLine": "LF",
    "noEmitOnError": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitOverride": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "pretty": true,
    "skipLibCheck": true,
    "sourceMap": true,
    "strict": true,
    "strictNullChecks": true,
    "target": "es2017"
  },
  "include": [
    "src/**/*.ts",
  ],
  "exclude": [
    "node_modules"
  ]
}