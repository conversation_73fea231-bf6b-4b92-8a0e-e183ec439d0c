{"name": "open-telemetry/operator-autoinstrumentation-php", "description": "OpenTelemetry PHP auto-instrumentation packages to include in the image used by OpenTelemetry Operator for Kubernetes", "type": "project", "require": {"open-telemetry/exporter-otlp": "1.1.0", "open-telemetry/opentelemetry-auto-guzzle": "1.0.1", "open-telemetry/opentelemetry-auto-http-async": "1.0.1", "open-telemetry/opentelemetry-auto-laravel": "1.0.1", "open-telemetry/opentelemetry-auto-pdo": "0.0.16", "open-telemetry/opentelemetry-auto-psr15": "1.0.6", "open-telemetry/opentelemetry-auto-psr18": "1.0.4", "open-telemetry/opentelemetry-auto-slim": "1.0.7", "open-telemetry/opentelemetry-auto-symfony": "1.0.0beta30", "open-telemetry/opentelemetry-auto-wordpress": "0.0.16", "open-telemetry/sdk": "1.1.2", "php-http/guzzle7-adapter": "1.0.0"}, "provide": {"psr/http-client": "*", "psr/http-server-middleware": "*", "laravel/framework": "*", "slim/slim": "*", "symfony/http-client-contracts": "*", "symfony/http-kernel": "*"}, "config": {"process-timeout": 0, "sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "php-http/discovery": true}}}