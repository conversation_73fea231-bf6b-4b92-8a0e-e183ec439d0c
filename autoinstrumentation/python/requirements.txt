opentelemetry-distro==0.55b1

# We add an upper limit to urllib3 version else Python 3.8 support is dropped.
# TODO: Update autoinstrumentation image builds to support all supported Python versions
#       https://github.com/open-telemetry/opentelemetry-operator/issues/3712
urllib3 <2.5.1
# We don't use the distro[otlp] option which automatically includes exporters since gRPC is not appropriate for
# injected auto-instrumentation, where it has a strict dependency on the OS / Python version the artifact is built for.
opentelemetry-exporter-otlp-proto-http==1.34.1
opentelemetry-exporter-prometheus==0.55b1

opentelemetry-propagator-b3==1.34.1
opentelemetry-propagator-jaeger==1.34.1
opentelemetry-propagator-aws-xray==1.0.2

opentelemetry-instrumentation==0.55b1
opentelemetry-propagator-ot-trace==0.55b1

# Copied in from https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/instrumentation
opentelemetry-instrumentation-aio-pika==0.55b1
opentelemetry-instrumentation-aiohttp-client==0.55b1
opentelemetry-instrumentation-aiohttp-server==0.55b1
opentelemetry-instrumentation-aiokafka==0.55b1
opentelemetry-instrumentation-aiopg==0.55b1
opentelemetry-instrumentation-asgi==0.55b1
opentelemetry-instrumentation-asyncio==0.55b1
opentelemetry-instrumentation-asyncpg==0.55b1
opentelemetry-instrumentation-aws-lambda==0.55b1
opentelemetry-instrumentation-boto==0.55b1
opentelemetry-instrumentation-boto3sqs==0.55b1
opentelemetry-instrumentation-botocore==0.55b1
opentelemetry-instrumentation-cassandra==0.55b1
opentelemetry-instrumentation-celery==0.55b1
opentelemetry-instrumentation-click==0.55b1
opentelemetry-instrumentation-confluent-kafka==0.55b1
opentelemetry-instrumentation-dbapi==0.55b1
opentelemetry-instrumentation-django==0.55b1
opentelemetry-instrumentation-elasticsearch==0.55b1
opentelemetry-instrumentation-falcon==0.55b1
opentelemetry-instrumentation-fastapi==0.55b1
opentelemetry-instrumentation-flask==0.55b1
opentelemetry-instrumentation-grpc==0.55b1
opentelemetry-instrumentation-httpx==0.55b1
opentelemetry-instrumentation-jinja2==0.55b1
opentelemetry-instrumentation-kafka-python==0.55b1
opentelemetry-instrumentation-logging==0.55b1
opentelemetry-instrumentation-mysql==0.55b1
opentelemetry-instrumentation-mysqlclient==0.55b1
opentelemetry-instrumentation-pika==0.55b1
opentelemetry-instrumentation-psycopg==0.55b1
opentelemetry-instrumentation-psycopg2==0.55b1
opentelemetry-instrumentation-pymemcache==0.55b1
opentelemetry-instrumentation-pymongo==0.55b1
opentelemetry-instrumentation-pymysql==0.55b1
opentelemetry-instrumentation-pyramid==0.55b1
opentelemetry-instrumentation-redis==0.55b1
opentelemetry-instrumentation-remoulade==0.55b1
opentelemetry-instrumentation-requests==0.55b1
opentelemetry-instrumentation-sqlalchemy==0.55b1
opentelemetry-instrumentation-sqlite3==0.55b1
opentelemetry-instrumentation-starlette==0.55b1
opentelemetry-instrumentation-system-metrics==0.55b1
opentelemetry-instrumentation-threading==0.55b1
opentelemetry-instrumentation-tornado==0.55b1
opentelemetry-instrumentation-tortoiseorm==0.55b1
opentelemetry-instrumentation-urllib==0.55b1
opentelemetry-instrumentation-urllib3==0.55b1
opentelemetry-instrumentation-wsgi==0.55b1
