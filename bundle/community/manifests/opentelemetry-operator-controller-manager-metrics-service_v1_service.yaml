apiVersion: v1
kind: Service
metadata:
  creationTimestamp: null
  labels:
    app.kubernetes.io/name: opentelemetry-operator
    control-plane: controller-manager
  name: opentelemetry-operator-controller-manager-metrics-service
spec:
  ports:
  - name: https
    port: 8443
    protocol: TCP
    targetPort: https
  selector:
    app.kubernetes.io/name: opentelemetry-operator
    control-plane: controller-manager
status:
  loadBalancer: {}
