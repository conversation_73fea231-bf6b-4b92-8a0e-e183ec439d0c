apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.18.0
  creationTimestamp: null
  labels:
    app.kubernetes.io/name: opentelemetry-operator
  name: instrumentations.opentelemetry.io
spec:
  group: opentelemetry.io
  names:
    kind: Instrumentation
    listKind: InstrumentationList
    plural: instrumentations
    shortNames:
    - otelinst
    - otelinsts
    singular: instrumentation
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .spec.exporter.endpoint
      name: Endpoint
      type: string
    - jsonPath: .spec.sampler.type
      name: Sampler
      type: string
    - jsonPath: .spec.sampler.argument
      name: Sampler Arg
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              apacheHttpd:
                properties:
                  attrs:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  configPath:
                    type: string
                  env:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  image:
                    type: string
                  resourceRequirements:
                    properties:
                      claims:
                        items:
                          properties:
                            name:
                              type: string
                            request:
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                    type: object
                  version:
                    type: string
                  volumeClaimTemplate:
                    properties:
                      metadata:
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          finalizers:
                            items:
                              type: string
                            type: array
                          labels:
                            additionalProperties:
                              type: string
                            type: object
                          name:
                            type: string
                          namespace:
                            type: string
                        type: object
                      spec:
                        properties:
                          accessModes:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          dataSource:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          dataSourceRef:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                              namespace:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                          resources:
                            properties:
                              limits:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                              requests:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                            type: object
                          selector:
                            properties:
                              matchExpressions:
                                items:
                                  properties:
                                    key:
                                      type: string
                                    operator:
                                      type: string
                                    values:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  required:
                                  - key
                                  - operator
                                  type: object
                                type: array
                                x-kubernetes-list-type: atomic
                              matchLabels:
                                additionalProperties:
                                  type: string
                                type: object
                            type: object
                            x-kubernetes-map-type: atomic
                          storageClassName:
                            type: string
                          volumeAttributesClassName:
                            type: string
                          volumeMode:
                            type: string
                          volumeName:
                            type: string
                        type: object
                    required:
                    - spec
                    type: object
                  volumeLimitSize:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                type: object
              defaults:
                properties:
                  useLabelsForResourceAttributes:
                    type: boolean
                type: object
              dotnet:
                properties:
                  env:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  image:
                    type: string
                  resourceRequirements:
                    properties:
                      claims:
                        items:
                          properties:
                            name:
                              type: string
                            request:
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                    type: object
                  volumeClaimTemplate:
                    properties:
                      metadata:
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          finalizers:
                            items:
                              type: string
                            type: array
                          labels:
                            additionalProperties:
                              type: string
                            type: object
                          name:
                            type: string
                          namespace:
                            type: string
                        type: object
                      spec:
                        properties:
                          accessModes:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          dataSource:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          dataSourceRef:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                              namespace:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                          resources:
                            properties:
                              limits:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                              requests:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                            type: object
                          selector:
                            properties:
                              matchExpressions:
                                items:
                                  properties:
                                    key:
                                      type: string
                                    operator:
                                      type: string
                                    values:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  required:
                                  - key
                                  - operator
                                  type: object
                                type: array
                                x-kubernetes-list-type: atomic
                              matchLabels:
                                additionalProperties:
                                  type: string
                                type: object
                            type: object
                            x-kubernetes-map-type: atomic
                          storageClassName:
                            type: string
                          volumeAttributesClassName:
                            type: string
                          volumeMode:
                            type: string
                          volumeName:
                            type: string
                        type: object
                    required:
                    - spec
                    type: object
                  volumeLimitSize:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                type: object
              env:
                items:
                  properties:
                    name:
                      type: string
                    value:
                      type: string
                    valueFrom:
                      properties:
                        configMapKeyRef:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        fieldRef:
                          properties:
                            apiVersion:
                              type: string
                            fieldPath:
                              type: string
                          required:
                          - fieldPath
                          type: object
                          x-kubernetes-map-type: atomic
                        resourceFieldRef:
                          properties:
                            containerName:
                              type: string
                            divisor:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            resource:
                              type: string
                          required:
                          - resource
                          type: object
                          x-kubernetes-map-type: atomic
                        secretKeyRef:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                      type: object
                  required:
                  - name
                  type: object
                type: array
              exporter:
                properties:
                  endpoint:
                    type: string
                  tls:
                    properties:
                      ca_file:
                        type: string
                      cert_file:
                        type: string
                      configMapName:
                        type: string
                      key_file:
                        type: string
                      secretName:
                        type: string
                    type: object
                type: object
              go:
                properties:
                  env:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  image:
                    type: string
                  resourceRequirements:
                    properties:
                      claims:
                        items:
                          properties:
                            name:
                              type: string
                            request:
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                    type: object
                  volumeClaimTemplate:
                    properties:
                      metadata:
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          finalizers:
                            items:
                              type: string
                            type: array
                          labels:
                            additionalProperties:
                              type: string
                            type: object
                          name:
                            type: string
                          namespace:
                            type: string
                        type: object
                      spec:
                        properties:
                          accessModes:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          dataSource:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          dataSourceRef:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                              namespace:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                          resources:
                            properties:
                              limits:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                              requests:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                            type: object
                          selector:
                            properties:
                              matchExpressions:
                                items:
                                  properties:
                                    key:
                                      type: string
                                    operator:
                                      type: string
                                    values:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  required:
                                  - key
                                  - operator
                                  type: object
                                type: array
                                x-kubernetes-list-type: atomic
                              matchLabels:
                                additionalProperties:
                                  type: string
                                type: object
                            type: object
                            x-kubernetes-map-type: atomic
                          storageClassName:
                            type: string
                          volumeAttributesClassName:
                            type: string
                          volumeMode:
                            type: string
                          volumeName:
                            type: string
                        type: object
                    required:
                    - spec
                    type: object
                  volumeLimitSize:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                type: object
              imagePullPolicy:
                type: string
              java:
                properties:
                  env:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  extensions:
                    items:
                      properties:
                        dir:
                          type: string
                        image:
                          type: string
                      required:
                      - dir
                      - image
                      type: object
                    type: array
                  image:
                    type: string
                  resources:
                    properties:
                      claims:
                        items:
                          properties:
                            name:
                              type: string
                            request:
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                    type: object
                  volumeClaimTemplate:
                    properties:
                      metadata:
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          finalizers:
                            items:
                              type: string
                            type: array
                          labels:
                            additionalProperties:
                              type: string
                            type: object
                          name:
                            type: string
                          namespace:
                            type: string
                        type: object
                      spec:
                        properties:
                          accessModes:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          dataSource:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          dataSourceRef:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                              namespace:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                          resources:
                            properties:
                              limits:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                              requests:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                            type: object
                          selector:
                            properties:
                              matchExpressions:
                                items:
                                  properties:
                                    key:
                                      type: string
                                    operator:
                                      type: string
                                    values:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  required:
                                  - key
                                  - operator
                                  type: object
                                type: array
                                x-kubernetes-list-type: atomic
                              matchLabels:
                                additionalProperties:
                                  type: string
                                type: object
                            type: object
                            x-kubernetes-map-type: atomic
                          storageClassName:
                            type: string
                          volumeAttributesClassName:
                            type: string
                          volumeMode:
                            type: string
                          volumeName:
                            type: string
                        type: object
                    required:
                    - spec
                    type: object
                  volumeLimitSize:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                type: object
              nginx:
                properties:
                  attrs:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  configFile:
                    type: string
                  env:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  image:
                    type: string
                  resourceRequirements:
                    properties:
                      claims:
                        items:
                          properties:
                            name:
                              type: string
                            request:
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                    type: object
                  volumeClaimTemplate:
                    properties:
                      metadata:
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          finalizers:
                            items:
                              type: string
                            type: array
                          labels:
                            additionalProperties:
                              type: string
                            type: object
                          name:
                            type: string
                          namespace:
                            type: string
                        type: object
                      spec:
                        properties:
                          accessModes:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          dataSource:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          dataSourceRef:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                              namespace:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                          resources:
                            properties:
                              limits:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                              requests:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                            type: object
                          selector:
                            properties:
                              matchExpressions:
                                items:
                                  properties:
                                    key:
                                      type: string
                                    operator:
                                      type: string
                                    values:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  required:
                                  - key
                                  - operator
                                  type: object
                                type: array
                                x-kubernetes-list-type: atomic
                              matchLabels:
                                additionalProperties:
                                  type: string
                                type: object
                            type: object
                            x-kubernetes-map-type: atomic
                          storageClassName:
                            type: string
                          volumeAttributesClassName:
                            type: string
                          volumeMode:
                            type: string
                          volumeName:
                            type: string
                        type: object
                    required:
                    - spec
                    type: object
                  volumeLimitSize:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                type: object
              nodejs:
                properties:
                  env:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  image:
                    type: string
                  resourceRequirements:
                    properties:
                      claims:
                        items:
                          properties:
                            name:
                              type: string
                            request:
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                    type: object
                  volumeClaimTemplate:
                    properties:
                      metadata:
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          finalizers:
                            items:
                              type: string
                            type: array
                          labels:
                            additionalProperties:
                              type: string
                            type: object
                          name:
                            type: string
                          namespace:
                            type: string
                        type: object
                      spec:
                        properties:
                          accessModes:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          dataSource:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          dataSourceRef:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                              namespace:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                          resources:
                            properties:
                              limits:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                              requests:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                            type: object
                          selector:
                            properties:
                              matchExpressions:
                                items:
                                  properties:
                                    key:
                                      type: string
                                    operator:
                                      type: string
                                    values:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  required:
                                  - key
                                  - operator
                                  type: object
                                type: array
                                x-kubernetes-list-type: atomic
                              matchLabels:
                                additionalProperties:
                                  type: string
                                type: object
                            type: object
                            x-kubernetes-map-type: atomic
                          storageClassName:
                            type: string
                          volumeAttributesClassName:
                            type: string
                          volumeMode:
                            type: string
                          volumeName:
                            type: string
                        type: object
                    required:
                    - spec
                    type: object
                  volumeLimitSize:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                type: object
              propagators:
                items:
                  enum:
                  - tracecontext
                  - baggage
                  - b3
                  - b3multi
                  - jaeger
                  - xray
                  - ottrace
                  - none
                  type: string
                type: array
              python:
                properties:
                  env:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  image:
                    type: string
                  resourceRequirements:
                    properties:
                      claims:
                        items:
                          properties:
                            name:
                              type: string
                            request:
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                    type: object
                  volumeClaimTemplate:
                    properties:
                      metadata:
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          finalizers:
                            items:
                              type: string
                            type: array
                          labels:
                            additionalProperties:
                              type: string
                            type: object
                          name:
                            type: string
                          namespace:
                            type: string
                        type: object
                      spec:
                        properties:
                          accessModes:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          dataSource:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          dataSourceRef:
                            properties:
                              apiGroup:
                                type: string
                              kind:
                                type: string
                              name:
                                type: string
                              namespace:
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                          resources:
                            properties:
                              limits:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                              requests:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type: object
                            type: object
                          selector:
                            properties:
                              matchExpressions:
                                items:
                                  properties:
                                    key:
                                      type: string
                                    operator:
                                      type: string
                                    values:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  required:
                                  - key
                                  - operator
                                  type: object
                                type: array
                                x-kubernetes-list-type: atomic
                              matchLabels:
                                additionalProperties:
                                  type: string
                                type: object
                            type: object
                            x-kubernetes-map-type: atomic
                          storageClassName:
                            type: string
                          volumeAttributesClassName:
                            type: string
                          volumeMode:
                            type: string
                          volumeName:
                            type: string
                        type: object
                    required:
                    - spec
                    type: object
                  volumeLimitSize:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                type: object
              resource:
                properties:
                  addK8sUIDAttributes:
                    type: boolean
                  resourceAttributes:
                    additionalProperties:
                      type: string
                    type: object
                type: object
              sampler:
                properties:
                  argument:
                    type: string
                  type:
                    enum:
                    - always_on
                    - always_off
                    - traceidratio
                    - parentbased_always_on
                    - parentbased_always_off
                    - parentbased_traceidratio
                    - jaeger_remote
                    - xray
                    type: string
                type: object
            type: object
          status:
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: null
  storedVersions: null
