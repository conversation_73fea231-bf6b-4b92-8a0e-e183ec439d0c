apiVersion: opentelemetry.io/v1beta1
kind: OpenTelemetryCollector
metadata:
  name: simplest
  labels:
    "opentelemetry.io/opamp-managed": "true"
  creationTimestamp: "1970-01-01T00:00:00Z"
spec:
  config:
    receivers:
      otlp:
        protocols:
          grpc:
          http:
    processors:
      memory_limiter:
        check_interval: 1s
        limit_percentage: 75
        spike_limit_percentage: 15
      batch:
        send_batch_size: 10000
        timeout: 10s
    
    exporters:
      debug:
    
    service:
      pipelines:
        traces:
          receivers: [otlp]
          exporters: [debug]
