apiVersion: opentelemetry.io/v1alpha1
kind: OpenTelemetryCollector
metadata:
  name: simplest
  labels:
    opentelemetry.io/opamp-managed: "true"
spec:
  config: |
    receivers:
      otlp:
        protocols:
          grpc:
          http:
    processors:
      memory_limiter:
        check_interval: 1s
        limit_percentage: 75
        spike_limit_percentage: 15
      batch:
        send_batch_size: 10000
        timeout: 10s
    
    exporters:
      debug:
    
    service:
      pipelines:
        traces:
          receivers: [otlp]
          processors: []
          exporters: [debug]
