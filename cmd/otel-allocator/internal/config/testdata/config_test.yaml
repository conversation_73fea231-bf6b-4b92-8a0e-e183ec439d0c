collector_namespace: default
collector_selector:
  matchlabels:
    app.kubernetes.io/instance: default.test
    app.kubernetes.io/managed-by: opentelemetry-operator
prometheus_cr:
  enabled: true
  scrape_interval: 60s
collector_not_ready_grace_period: 30s
https:
  enabled: true
  listen_addr: :8443
  ca_file_path: /path/to/ca.pem
  tls_cert_file_path: /path/to/cert.pem
  tls_key_file_path: /path/to/key.pem
config:
  scrape_configs:
  - job_name: prometheus

    file_sd_configs:
    - files:
      - ./file_sd_test.json

    static_configs:
    - targets: ["prom.domain:9001", "prom.domain:9002", "prom.domain:9003"]
      labels:
        my: label
