collector_namespace: default
collector_selector:
  matchLabels:
    app.kubernetes.io/instance: default.test
    app.kubernetes.io/managed-by: opentelemetry-operator
prometheus_cr:
  pod_monitor_selector:
    matchLabels:
      release: test
  service_monitor_selector:
    matchLabels:
      release: test
config:
  scrape_configs:
    - job_name: prometheus
      static_configs:
        - targets: ["prom.domain:9001", "prom.domain:9002", "prom.domain:9003"]
          labels:
            my: label