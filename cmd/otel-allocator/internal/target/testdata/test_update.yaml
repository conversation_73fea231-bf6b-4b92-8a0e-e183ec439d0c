collector_selector:
  matchlabels:
    app.kubernetes.io/instance: default.test
    app.kubernetes.io/managed-by: opentelemetry-operator
config:
  scrape_configs:
    - job_name: prometheus

      file_sd_configs:
        - files:
            - ../config/testdata/file_sd_test.json
      static_configs:
        - targets: ["prom.domain:9004", "prom.domain:9005"]
          labels:
            my: other-label
