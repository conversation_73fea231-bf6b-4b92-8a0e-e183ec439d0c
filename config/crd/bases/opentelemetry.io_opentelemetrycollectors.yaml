---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.18.0
  name: opentelemetrycollectors.opentelemetry.io
spec:
  group: opentelemetry.io
  names:
    kind: OpenTelemetryCollector
    listKind: OpenTelemetryCollectorList
    plural: opentelemetrycollectors
    shortNames:
    - otelcol
    - otelcols
    singular: opentelemetrycollector
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Deployment Mode
      jsonPath: .spec.mode
      name: Mode
      type: string
    - description: OpenTelemetry Version
      jsonPath: .status.version
      name: Version
      type: string
    - jsonPath: .status.scale.statusReplicas
      name: Ready
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .status.image
      name: Image
      type: string
    - description: Management State
      jsonPath: .spec.managementState
      name: Management
      type: string
    deprecated: true
    deprecationWarning: OpenTelemetryCollector v1alpha1 is deprecated. Migrate to
      v1beta1.
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              additionalContainers:
                items:
                  properties:
                    args:
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    command:
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    env:
                      items:
                        properties:
                          name:
                            type: string
                          value:
                            type: string
                          valueFrom:
                            properties:
                              configMapKeyRef:
                                properties:
                                  key:
                                    type: string
                                  name:
                                    default: ""
                                    type: string
                                  optional:
                                    type: boolean
                                required:
                                - key
                                type: object
                                x-kubernetes-map-type: atomic
                              fieldRef:
                                properties:
                                  apiVersion:
                                    type: string
                                  fieldPath:
                                    type: string
                                required:
                                - fieldPath
                                type: object
                                x-kubernetes-map-type: atomic
                              resourceFieldRef:
                                properties:
                                  containerName:
                                    type: string
                                  divisor:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  resource:
                                    type: string
                                required:
                                - resource
                                type: object
                                x-kubernetes-map-type: atomic
                              secretKeyRef:
                                properties:
                                  key:
                                    type: string
                                  name:
                                    default: ""
                                    type: string
                                  optional:
                                    type: boolean
                                required:
                                - key
                                type: object
                                x-kubernetes-map-type: atomic
                            type: object
                        required:
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - name
                      x-kubernetes-list-type: map
                    envFrom:
                      items:
                        properties:
                          configMapRef:
                            properties:
                              name:
                                default: ""
                                type: string
                              optional:
                                type: boolean
                            type: object
                            x-kubernetes-map-type: atomic
                          prefix:
                            type: string
                          secretRef:
                            properties:
                              name:
                                default: ""
                                type: string
                              optional:
                                type: boolean
                            type: object
                            x-kubernetes-map-type: atomic
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    image:
                      type: string
                    imagePullPolicy:
                      type: string
                    lifecycle:
                      properties:
                        postStart:
                          properties:
                            exec:
                              properties:
                                command:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            httpGet:
                              properties:
                                host:
                                  type: string
                                httpHeaders:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                path:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                                scheme:
                                  type: string
                              required:
                              - port
                              type: object
                            sleep:
                              properties:
                                seconds:
                                  format: int64
                                  type: integer
                              required:
                              - seconds
                              type: object
                            tcpSocket:
                              properties:
                                host:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                              required:
                              - port
                              type: object
                          type: object
                        preStop:
                          properties:
                            exec:
                              properties:
                                command:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            httpGet:
                              properties:
                                host:
                                  type: string
                                httpHeaders:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                path:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                                scheme:
                                  type: string
                              required:
                              - port
                              type: object
                            sleep:
                              properties:
                                seconds:
                                  format: int64
                                  type: integer
                              required:
                              - seconds
                              type: object
                            tcpSocket:
                              properties:
                                host:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                              required:
                              - port
                              type: object
                          type: object
                      type: object
                    livenessProbe:
                      properties:
                        exec:
                          properties:
                            command:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          format: int32
                          type: integer
                        grpc:
                          properties:
                            port:
                              format: int32
                              type: integer
                            service:
                              default: ""
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          properties:
                            host:
                              type: string
                            httpHeaders:
                              items:
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            scheme:
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          format: int32
                          type: integer
                        periodSeconds:
                          format: int32
                          type: integer
                        successThreshold:
                          format: int32
                          type: integer
                        tcpSocket:
                          properties:
                            host:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          format: int64
                          type: integer
                        timeoutSeconds:
                          format: int32
                          type: integer
                      type: object
                    name:
                      type: string
                    ports:
                      items:
                        properties:
                          containerPort:
                            format: int32
                            type: integer
                          hostIP:
                            type: string
                          hostPort:
                            format: int32
                            type: integer
                          name:
                            type: string
                          protocol:
                            default: TCP
                            type: string
                        required:
                        - containerPort
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - containerPort
                      - protocol
                      x-kubernetes-list-type: map
                    readinessProbe:
                      properties:
                        exec:
                          properties:
                            command:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          format: int32
                          type: integer
                        grpc:
                          properties:
                            port:
                              format: int32
                              type: integer
                            service:
                              default: ""
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          properties:
                            host:
                              type: string
                            httpHeaders:
                              items:
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            scheme:
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          format: int32
                          type: integer
                        periodSeconds:
                          format: int32
                          type: integer
                        successThreshold:
                          format: int32
                          type: integer
                        tcpSocket:
                          properties:
                            host:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          format: int64
                          type: integer
                        timeoutSeconds:
                          format: int32
                          type: integer
                      type: object
                    resizePolicy:
                      items:
                        properties:
                          resourceName:
                            type: string
                          restartPolicy:
                            type: string
                        required:
                        - resourceName
                        - restartPolicy
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    resources:
                      properties:
                        claims:
                          items:
                            properties:
                              name:
                                type: string
                              request:
                                type: string
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        limits:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          type: object
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          type: object
                      type: object
                    restartPolicy:
                      type: string
                    securityContext:
                      properties:
                        allowPrivilegeEscalation:
                          type: boolean
                        appArmorProfile:
                          properties:
                            localhostProfile:
                              type: string
                            type:
                              type: string
                          required:
                          - type
                          type: object
                        capabilities:
                          properties:
                            add:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            drop:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        privileged:
                          type: boolean
                        procMount:
                          type: string
                        readOnlyRootFilesystem:
                          type: boolean
                        runAsGroup:
                          format: int64
                          type: integer
                        runAsNonRoot:
                          type: boolean
                        runAsUser:
                          format: int64
                          type: integer
                        seLinuxOptions:
                          properties:
                            level:
                              type: string
                            role:
                              type: string
                            type:
                              type: string
                            user:
                              type: string
                          type: object
                        seccompProfile:
                          properties:
                            localhostProfile:
                              type: string
                            type:
                              type: string
                          required:
                          - type
                          type: object
                        windowsOptions:
                          properties:
                            gmsaCredentialSpec:
                              type: string
                            gmsaCredentialSpecName:
                              type: string
                            hostProcess:
                              type: boolean
                            runAsUserName:
                              type: string
                          type: object
                      type: object
                    startupProbe:
                      properties:
                        exec:
                          properties:
                            command:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          format: int32
                          type: integer
                        grpc:
                          properties:
                            port:
                              format: int32
                              type: integer
                            service:
                              default: ""
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          properties:
                            host:
                              type: string
                            httpHeaders:
                              items:
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            scheme:
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          format: int32
                          type: integer
                        periodSeconds:
                          format: int32
                          type: integer
                        successThreshold:
                          format: int32
                          type: integer
                        tcpSocket:
                          properties:
                            host:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          format: int64
                          type: integer
                        timeoutSeconds:
                          format: int32
                          type: integer
                      type: object
                    stdin:
                      type: boolean
                    stdinOnce:
                      type: boolean
                    terminationMessagePath:
                      type: string
                    terminationMessagePolicy:
                      type: string
                    tty:
                      type: boolean
                    volumeDevices:
                      items:
                        properties:
                          devicePath:
                            type: string
                          name:
                            type: string
                        required:
                        - devicePath
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - devicePath
                      x-kubernetes-list-type: map
                    volumeMounts:
                      items:
                        properties:
                          mountPath:
                            type: string
                          mountPropagation:
                            type: string
                          name:
                            type: string
                          readOnly:
                            type: boolean
                          recursiveReadOnly:
                            type: string
                          subPath:
                            type: string
                          subPathExpr:
                            type: string
                        required:
                        - mountPath
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - mountPath
                      x-kubernetes-list-type: map
                    workingDir:
                      type: string
                  required:
                  - name
                  type: object
                type: array
              affinity:
                properties:
                  nodeAffinity:
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        items:
                          properties:
                            preference:
                              properties:
                                matchExpressions:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchFields:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                              x-kubernetes-map-type: atomic
                            weight:
                              format: int32
                              type: integer
                          required:
                          - preference
                          - weight
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      requiredDuringSchedulingIgnoredDuringExecution:
                        properties:
                          nodeSelectorTerms:
                            items:
                              properties:
                                matchExpressions:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchFields:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                              x-kubernetes-map-type: atomic
                            type: array
                            x-kubernetes-list-type: atomic
                        required:
                        - nodeSelectorTerms
                        type: object
                        x-kubernetes-map-type: atomic
                    type: object
                  podAffinity:
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        items:
                          properties:
                            podAffinityTerm:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            weight:
                              format: int32
                              type: integer
                          required:
                          - podAffinityTerm
                          - weight
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      requiredDuringSchedulingIgnoredDuringExecution:
                        items:
                          properties:
                            labelSelector:
                              properties:
                                matchExpressions:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            matchLabelKeys:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            mismatchLabelKeys:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            namespaceSelector:
                              properties:
                                matchExpressions:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            topologyKey:
                              type: string
                          required:
                          - topologyKey
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  podAntiAffinity:
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        items:
                          properties:
                            podAffinityTerm:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            weight:
                              format: int32
                              type: integer
                          required:
                          - podAffinityTerm
                          - weight
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      requiredDuringSchedulingIgnoredDuringExecution:
                        items:
                          properties:
                            labelSelector:
                              properties:
                                matchExpressions:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            matchLabelKeys:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            mismatchLabelKeys:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            namespaceSelector:
                              properties:
                                matchExpressions:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            topologyKey:
                              type: string
                          required:
                          - topologyKey
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                type: object
              args:
                additionalProperties:
                  type: string
                type: object
              autoscaler:
                properties:
                  behavior:
                    properties:
                      scaleDown:
                        properties:
                          policies:
                            items:
                              properties:
                                periodSeconds:
                                  format: int32
                                  type: integer
                                type:
                                  type: string
                                value:
                                  format: int32
                                  type: integer
                              required:
                              - periodSeconds
                              - type
                              - value
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          selectPolicy:
                            type: string
                          stabilizationWindowSeconds:
                            format: int32
                            type: integer
                        type: object
                      scaleUp:
                        properties:
                          policies:
                            items:
                              properties:
                                periodSeconds:
                                  format: int32
                                  type: integer
                                type:
                                  type: string
                                value:
                                  format: int32
                                  type: integer
                              required:
                              - periodSeconds
                              - type
                              - value
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          selectPolicy:
                            type: string
                          stabilizationWindowSeconds:
                            format: int32
                            type: integer
                        type: object
                    type: object
                  maxReplicas:
                    format: int32
                    type: integer
                  metrics:
                    items:
                      properties:
                        pods:
                          properties:
                            metric:
                              properties:
                                name:
                                  type: string
                                selector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                              required:
                              - name
                              type: object
                            target:
                              properties:
                                averageUtilization:
                                  format: int32
                                  type: integer
                                averageValue:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type:
                                  type: string
                                value:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                              required:
                              - type
                              type: object
                          required:
                          - metric
                          - target
                          type: object
                        type:
                          type: string
                      required:
                      - type
                      type: object
                    type: array
                  minReplicas:
                    format: int32
                    type: integer
                  targetCPUUtilization:
                    format: int32
                    type: integer
                  targetMemoryUtilization:
                    format: int32
                    type: integer
                type: object
              config:
                type: string
              configmaps:
                items:
                  properties:
                    mountpath:
                      type: string
                    name:
                      type: string
                  required:
                  - mountpath
                  - name
                  type: object
                type: array
              deploymentUpdateStrategy:
                properties:
                  rollingUpdate:
                    properties:
                      maxSurge:
                        anyOf:
                        - type: integer
                        - type: string
                        x-kubernetes-int-or-string: true
                      maxUnavailable:
                        anyOf:
                        - type: integer
                        - type: string
                        x-kubernetes-int-or-string: true
                    type: object
                  type:
                    type: string
                type: object
              env:
                items:
                  properties:
                    name:
                      type: string
                    value:
                      type: string
                    valueFrom:
                      properties:
                        configMapKeyRef:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        fieldRef:
                          properties:
                            apiVersion:
                              type: string
                            fieldPath:
                              type: string
                          required:
                          - fieldPath
                          type: object
                          x-kubernetes-map-type: atomic
                        resourceFieldRef:
                          properties:
                            containerName:
                              type: string
                            divisor:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            resource:
                              type: string
                          required:
                          - resource
                          type: object
                          x-kubernetes-map-type: atomic
                        secretKeyRef:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                      type: object
                  required:
                  - name
                  type: object
                type: array
              envFrom:
                items:
                  properties:
                    configMapRef:
                      properties:
                        name:
                          default: ""
                          type: string
                        optional:
                          type: boolean
                      type: object
                      x-kubernetes-map-type: atomic
                    prefix:
                      type: string
                    secretRef:
                      properties:
                        name:
                          default: ""
                          type: string
                        optional:
                          type: boolean
                      type: object
                      x-kubernetes-map-type: atomic
                  type: object
                type: array
              hostNetwork:
                type: boolean
              image:
                type: string
              imagePullPolicy:
                type: string
              ingress:
                properties:
                  annotations:
                    additionalProperties:
                      type: string
                    type: object
                  hostname:
                    type: string
                  ingressClassName:
                    type: string
                  route:
                    properties:
                      termination:
                        enum:
                        - insecure
                        - edge
                        - passthrough
                        - reencrypt
                        type: string
                    type: object
                  ruleType:
                    enum:
                    - path
                    - subdomain
                    type: string
                  tls:
                    items:
                      properties:
                        hosts:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        secretName:
                          type: string
                      type: object
                    type: array
                  type:
                    enum:
                    - ingress
                    - route
                    type: string
                type: object
              initContainers:
                items:
                  properties:
                    args:
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    command:
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    env:
                      items:
                        properties:
                          name:
                            type: string
                          value:
                            type: string
                          valueFrom:
                            properties:
                              configMapKeyRef:
                                properties:
                                  key:
                                    type: string
                                  name:
                                    default: ""
                                    type: string
                                  optional:
                                    type: boolean
                                required:
                                - key
                                type: object
                                x-kubernetes-map-type: atomic
                              fieldRef:
                                properties:
                                  apiVersion:
                                    type: string
                                  fieldPath:
                                    type: string
                                required:
                                - fieldPath
                                type: object
                                x-kubernetes-map-type: atomic
                              resourceFieldRef:
                                properties:
                                  containerName:
                                    type: string
                                  divisor:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  resource:
                                    type: string
                                required:
                                - resource
                                type: object
                                x-kubernetes-map-type: atomic
                              secretKeyRef:
                                properties:
                                  key:
                                    type: string
                                  name:
                                    default: ""
                                    type: string
                                  optional:
                                    type: boolean
                                required:
                                - key
                                type: object
                                x-kubernetes-map-type: atomic
                            type: object
                        required:
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - name
                      x-kubernetes-list-type: map
                    envFrom:
                      items:
                        properties:
                          configMapRef:
                            properties:
                              name:
                                default: ""
                                type: string
                              optional:
                                type: boolean
                            type: object
                            x-kubernetes-map-type: atomic
                          prefix:
                            type: string
                          secretRef:
                            properties:
                              name:
                                default: ""
                                type: string
                              optional:
                                type: boolean
                            type: object
                            x-kubernetes-map-type: atomic
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    image:
                      type: string
                    imagePullPolicy:
                      type: string
                    lifecycle:
                      properties:
                        postStart:
                          properties:
                            exec:
                              properties:
                                command:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            httpGet:
                              properties:
                                host:
                                  type: string
                                httpHeaders:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                path:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                                scheme:
                                  type: string
                              required:
                              - port
                              type: object
                            sleep:
                              properties:
                                seconds:
                                  format: int64
                                  type: integer
                              required:
                              - seconds
                              type: object
                            tcpSocket:
                              properties:
                                host:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                              required:
                              - port
                              type: object
                          type: object
                        preStop:
                          properties:
                            exec:
                              properties:
                                command:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            httpGet:
                              properties:
                                host:
                                  type: string
                                httpHeaders:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                path:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                                scheme:
                                  type: string
                              required:
                              - port
                              type: object
                            sleep:
                              properties:
                                seconds:
                                  format: int64
                                  type: integer
                              required:
                              - seconds
                              type: object
                            tcpSocket:
                              properties:
                                host:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                              required:
                              - port
                              type: object
                          type: object
                      type: object
                    livenessProbe:
                      properties:
                        exec:
                          properties:
                            command:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          format: int32
                          type: integer
                        grpc:
                          properties:
                            port:
                              format: int32
                              type: integer
                            service:
                              default: ""
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          properties:
                            host:
                              type: string
                            httpHeaders:
                              items:
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            scheme:
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          format: int32
                          type: integer
                        periodSeconds:
                          format: int32
                          type: integer
                        successThreshold:
                          format: int32
                          type: integer
                        tcpSocket:
                          properties:
                            host:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          format: int64
                          type: integer
                        timeoutSeconds:
                          format: int32
                          type: integer
                      type: object
                    name:
                      type: string
                    ports:
                      items:
                        properties:
                          containerPort:
                            format: int32
                            type: integer
                          hostIP:
                            type: string
                          hostPort:
                            format: int32
                            type: integer
                          name:
                            type: string
                          protocol:
                            default: TCP
                            type: string
                        required:
                        - containerPort
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - containerPort
                      - protocol
                      x-kubernetes-list-type: map
                    readinessProbe:
                      properties:
                        exec:
                          properties:
                            command:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          format: int32
                          type: integer
                        grpc:
                          properties:
                            port:
                              format: int32
                              type: integer
                            service:
                              default: ""
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          properties:
                            host:
                              type: string
                            httpHeaders:
                              items:
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            scheme:
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          format: int32
                          type: integer
                        periodSeconds:
                          format: int32
                          type: integer
                        successThreshold:
                          format: int32
                          type: integer
                        tcpSocket:
                          properties:
                            host:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          format: int64
                          type: integer
                        timeoutSeconds:
                          format: int32
                          type: integer
                      type: object
                    resizePolicy:
                      items:
                        properties:
                          resourceName:
                            type: string
                          restartPolicy:
                            type: string
                        required:
                        - resourceName
                        - restartPolicy
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    resources:
                      properties:
                        claims:
                          items:
                            properties:
                              name:
                                type: string
                              request:
                                type: string
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        limits:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          type: object
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          type: object
                      type: object
                    restartPolicy:
                      type: string
                    securityContext:
                      properties:
                        allowPrivilegeEscalation:
                          type: boolean
                        appArmorProfile:
                          properties:
                            localhostProfile:
                              type: string
                            type:
                              type: string
                          required:
                          - type
                          type: object
                        capabilities:
                          properties:
                            add:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            drop:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        privileged:
                          type: boolean
                        procMount:
                          type: string
                        readOnlyRootFilesystem:
                          type: boolean
                        runAsGroup:
                          format: int64
                          type: integer
                        runAsNonRoot:
                          type: boolean
                        runAsUser:
                          format: int64
                          type: integer
                        seLinuxOptions:
                          properties:
                            level:
                              type: string
                            role:
                              type: string
                            type:
                              type: string
                            user:
                              type: string
                          type: object
                        seccompProfile:
                          properties:
                            localhostProfile:
                              type: string
                            type:
                              type: string
                          required:
                          - type
                          type: object
                        windowsOptions:
                          properties:
                            gmsaCredentialSpec:
                              type: string
                            gmsaCredentialSpecName:
                              type: string
                            hostProcess:
                              type: boolean
                            runAsUserName:
                              type: string
                          type: object
                      type: object
                    startupProbe:
                      properties:
                        exec:
                          properties:
                            command:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          format: int32
                          type: integer
                        grpc:
                          properties:
                            port:
                              format: int32
                              type: integer
                            service:
                              default: ""
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          properties:
                            host:
                              type: string
                            httpHeaders:
                              items:
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            scheme:
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          format: int32
                          type: integer
                        periodSeconds:
                          format: int32
                          type: integer
                        successThreshold:
                          format: int32
                          type: integer
                        tcpSocket:
                          properties:
                            host:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          format: int64
                          type: integer
                        timeoutSeconds:
                          format: int32
                          type: integer
                      type: object
                    stdin:
                      type: boolean
                    stdinOnce:
                      type: boolean
                    terminationMessagePath:
                      type: string
                    terminationMessagePolicy:
                      type: string
                    tty:
                      type: boolean
                    volumeDevices:
                      items:
                        properties:
                          devicePath:
                            type: string
                          name:
                            type: string
                        required:
                        - devicePath
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - devicePath
                      x-kubernetes-list-type: map
                    volumeMounts:
                      items:
                        properties:
                          mountPath:
                            type: string
                          mountPropagation:
                            type: string
                          name:
                            type: string
                          readOnly:
                            type: boolean
                          recursiveReadOnly:
                            type: string
                          subPath:
                            type: string
                          subPathExpr:
                            type: string
                        required:
                        - mountPath
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - mountPath
                      x-kubernetes-list-type: map
                    workingDir:
                      type: string
                  required:
                  - name
                  type: object
                type: array
              lifecycle:
                properties:
                  postStart:
                    properties:
                      exec:
                        properties:
                          command:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      httpGet:
                        properties:
                          host:
                            type: string
                          httpHeaders:
                            items:
                              properties:
                                name:
                                  type: string
                                value:
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          path:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                          scheme:
                            type: string
                        required:
                        - port
                        type: object
                      sleep:
                        properties:
                          seconds:
                            format: int64
                            type: integer
                        required:
                        - seconds
                        type: object
                      tcpSocket:
                        properties:
                          host:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                        required:
                        - port
                        type: object
                    type: object
                  preStop:
                    properties:
                      exec:
                        properties:
                          command:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      httpGet:
                        properties:
                          host:
                            type: string
                          httpHeaders:
                            items:
                              properties:
                                name:
                                  type: string
                                value:
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          path:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                          scheme:
                            type: string
                        required:
                        - port
                        type: object
                      sleep:
                        properties:
                          seconds:
                            format: int64
                            type: integer
                        required:
                        - seconds
                        type: object
                      tcpSocket:
                        properties:
                          host:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                        required:
                        - port
                        type: object
                    type: object
                type: object
              livenessProbe:
                properties:
                  failureThreshold:
                    format: int32
                    type: integer
                  initialDelaySeconds:
                    format: int32
                    type: integer
                  periodSeconds:
                    format: int32
                    type: integer
                  successThreshold:
                    format: int32
                    type: integer
                  terminationGracePeriodSeconds:
                    format: int64
                    type: integer
                  timeoutSeconds:
                    format: int32
                    type: integer
                type: object
              managementState:
                default: managed
                enum:
                - managed
                - unmanaged
                type: string
              maxReplicas:
                format: int32
                type: integer
              minReplicas:
                format: int32
                type: integer
              mode:
                enum:
                - daemonset
                - deployment
                - sidecar
                - statefulset
                type: string
              nodeSelector:
                additionalProperties:
                  type: string
                type: object
              observability:
                properties:
                  metrics:
                    properties:
                      DisablePrometheusAnnotations:
                        type: boolean
                      enableMetrics:
                        type: boolean
                    type: object
                type: object
              podAnnotations:
                additionalProperties:
                  type: string
                type: object
              podDisruptionBudget:
                properties:
                  maxUnavailable:
                    anyOf:
                    - type: integer
                    - type: string
                    x-kubernetes-int-or-string: true
                  minAvailable:
                    anyOf:
                    - type: integer
                    - type: string
                    x-kubernetes-int-or-string: true
                type: object
              podSecurityContext:
                properties:
                  appArmorProfile:
                    properties:
                      localhostProfile:
                        type: string
                      type:
                        type: string
                    required:
                    - type
                    type: object
                  fsGroup:
                    format: int64
                    type: integer
                  fsGroupChangePolicy:
                    type: string
                  runAsGroup:
                    format: int64
                    type: integer
                  runAsNonRoot:
                    type: boolean
                  runAsUser:
                    format: int64
                    type: integer
                  seLinuxChangePolicy:
                    type: string
                  seLinuxOptions:
                    properties:
                      level:
                        type: string
                      role:
                        type: string
                      type:
                        type: string
                      user:
                        type: string
                    type: object
                  seccompProfile:
                    properties:
                      localhostProfile:
                        type: string
                      type:
                        type: string
                    required:
                    - type
                    type: object
                  supplementalGroups:
                    items:
                      format: int64
                      type: integer
                    type: array
                    x-kubernetes-list-type: atomic
                  supplementalGroupsPolicy:
                    type: string
                  sysctls:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                      required:
                      - name
                      - value
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  windowsOptions:
                    properties:
                      gmsaCredentialSpec:
                        type: string
                      gmsaCredentialSpecName:
                        type: string
                      hostProcess:
                        type: boolean
                      runAsUserName:
                        type: string
                    type: object
                type: object
              ports:
                items:
                  properties:
                    appProtocol:
                      type: string
                    hostPort:
                      format: int32
                      type: integer
                    name:
                      type: string
                    nodePort:
                      format: int32
                      type: integer
                    port:
                      format: int32
                      type: integer
                    protocol:
                      default: TCP
                      type: string
                    targetPort:
                      anyOf:
                      - type: integer
                      - type: string
                      x-kubernetes-int-or-string: true
                  required:
                  - port
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              priorityClassName:
                type: string
              replicas:
                default: 1
                format: int32
                type: integer
              resources:
                properties:
                  claims:
                    items:
                      properties:
                        name:
                          type: string
                        request:
                          type: string
                      required:
                      - name
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - name
                    x-kubernetes-list-type: map
                  limits:
                    additionalProperties:
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                      x-kubernetes-int-or-string: true
                    type: object
                  requests:
                    additionalProperties:
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                      x-kubernetes-int-or-string: true
                    type: object
                type: object
              securityContext:
                properties:
                  allowPrivilegeEscalation:
                    type: boolean
                  appArmorProfile:
                    properties:
                      localhostProfile:
                        type: string
                      type:
                        type: string
                    required:
                    - type
                    type: object
                  capabilities:
                    properties:
                      add:
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                      drop:
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  privileged:
                    type: boolean
                  procMount:
                    type: string
                  readOnlyRootFilesystem:
                    type: boolean
                  runAsGroup:
                    format: int64
                    type: integer
                  runAsNonRoot:
                    type: boolean
                  runAsUser:
                    format: int64
                    type: integer
                  seLinuxOptions:
                    properties:
                      level:
                        type: string
                      role:
                        type: string
                      type:
                        type: string
                      user:
                        type: string
                    type: object
                  seccompProfile:
                    properties:
                      localhostProfile:
                        type: string
                      type:
                        type: string
                    required:
                    - type
                    type: object
                  windowsOptions:
                    properties:
                      gmsaCredentialSpec:
                        type: string
                      gmsaCredentialSpecName:
                        type: string
                      hostProcess:
                        type: boolean
                      runAsUserName:
                        type: string
                    type: object
                type: object
              serviceAccount:
                type: string
              serviceName:
                type: string
              shareProcessNamespace:
                type: boolean
              targetAllocator:
                properties:
                  affinity:
                    properties:
                      nodeAffinity:
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                preference:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchFields:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  type: object
                                  x-kubernetes-map-type: atomic
                                weight:
                                  format: int32
                                  type: integer
                              required:
                              - preference
                              - weight
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          requiredDuringSchedulingIgnoredDuringExecution:
                            properties:
                              nodeSelectorTerms:
                                items:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchFields:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  type: object
                                  x-kubernetes-map-type: atomic
                                type: array
                                x-kubernetes-list-type: atomic
                            required:
                            - nodeSelectorTerms
                            type: object
                            x-kubernetes-map-type: atomic
                        type: object
                      podAffinity:
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                podAffinityTerm:
                                  properties:
                                    labelSelector:
                                      properties:
                                        matchExpressions:
                                          items:
                                            properties:
                                              key:
                                                type: string
                                              operator:
                                                type: string
                                              values:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    matchLabelKeys:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    mismatchLabelKeys:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    namespaceSelector:
                                      properties:
                                        matchExpressions:
                                          items:
                                            properties:
                                              key:
                                                type: string
                                              operator:
                                                type: string
                                              values:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    namespaces:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    topologyKey:
                                      type: string
                                  required:
                                  - topologyKey
                                  type: object
                                weight:
                                  format: int32
                                  type: integer
                              required:
                              - podAffinityTerm
                              - weight
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          requiredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      podAntiAffinity:
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                podAffinityTerm:
                                  properties:
                                    labelSelector:
                                      properties:
                                        matchExpressions:
                                          items:
                                            properties:
                                              key:
                                                type: string
                                              operator:
                                                type: string
                                              values:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    matchLabelKeys:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    mismatchLabelKeys:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    namespaceSelector:
                                      properties:
                                        matchExpressions:
                                          items:
                                            properties:
                                              key:
                                                type: string
                                              operator:
                                                type: string
                                              values:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    namespaces:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    topologyKey:
                                      type: string
                                  required:
                                  - topologyKey
                                  type: object
                                weight:
                                  format: int32
                                  type: integer
                              required:
                              - podAffinityTerm
                              - weight
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          requiredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                    type: object
                  allocationStrategy:
                    default: consistent-hashing
                    enum:
                    - least-weighted
                    - consistent-hashing
                    - per-node
                    type: string
                  enabled:
                    type: boolean
                  env:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  filterStrategy:
                    default: relabel-config
                    type: string
                  image:
                    type: string
                  nodeSelector:
                    additionalProperties:
                      type: string
                    type: object
                  observability:
                    properties:
                      metrics:
                        properties:
                          DisablePrometheusAnnotations:
                            type: boolean
                          enableMetrics:
                            type: boolean
                        type: object
                    type: object
                  podDisruptionBudget:
                    properties:
                      maxUnavailable:
                        anyOf:
                        - type: integer
                        - type: string
                        x-kubernetes-int-or-string: true
                      minAvailable:
                        anyOf:
                        - type: integer
                        - type: string
                        x-kubernetes-int-or-string: true
                    type: object
                  podSecurityContext:
                    properties:
                      appArmorProfile:
                        properties:
                          localhostProfile:
                            type: string
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      fsGroup:
                        format: int64
                        type: integer
                      fsGroupChangePolicy:
                        type: string
                      runAsGroup:
                        format: int64
                        type: integer
                      runAsNonRoot:
                        type: boolean
                      runAsUser:
                        format: int64
                        type: integer
                      seLinuxChangePolicy:
                        type: string
                      seLinuxOptions:
                        properties:
                          level:
                            type: string
                          role:
                            type: string
                          type:
                            type: string
                          user:
                            type: string
                        type: object
                      seccompProfile:
                        properties:
                          localhostProfile:
                            type: string
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      supplementalGroups:
                        items:
                          format: int64
                          type: integer
                        type: array
                        x-kubernetes-list-type: atomic
                      supplementalGroupsPolicy:
                        type: string
                      sysctls:
                        items:
                          properties:
                            name:
                              type: string
                            value:
                              type: string
                          required:
                          - name
                          - value
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      windowsOptions:
                        properties:
                          gmsaCredentialSpec:
                            type: string
                          gmsaCredentialSpecName:
                            type: string
                          hostProcess:
                            type: boolean
                          runAsUserName:
                            type: string
                        type: object
                    type: object
                  prometheusCR:
                    properties:
                      enabled:
                        type: boolean
                      podMonitorSelector:
                        additionalProperties:
                          type: string
                        type: object
                      scrapeInterval:
                        default: 30s
                        format: duration
                        type: string
                      serviceMonitorSelector:
                        additionalProperties:
                          type: string
                        type: object
                    type: object
                  replicas:
                    format: int32
                    type: integer
                  resources:
                    properties:
                      claims:
                        items:
                          properties:
                            name:
                              type: string
                            request:
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                    type: object
                  securityContext:
                    properties:
                      allowPrivilegeEscalation:
                        type: boolean
                      appArmorProfile:
                        properties:
                          localhostProfile:
                            type: string
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      capabilities:
                        properties:
                          add:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          drop:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      privileged:
                        type: boolean
                      procMount:
                        type: string
                      readOnlyRootFilesystem:
                        type: boolean
                      runAsGroup:
                        format: int64
                        type: integer
                      runAsNonRoot:
                        type: boolean
                      runAsUser:
                        format: int64
                        type: integer
                      seLinuxOptions:
                        properties:
                          level:
                            type: string
                          role:
                            type: string
                          type:
                            type: string
                          user:
                            type: string
                        type: object
                      seccompProfile:
                        properties:
                          localhostProfile:
                            type: string
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      windowsOptions:
                        properties:
                          gmsaCredentialSpec:
                            type: string
                          gmsaCredentialSpecName:
                            type: string
                          hostProcess:
                            type: boolean
                          runAsUserName:
                            type: string
                        type: object
                    type: object
                  serviceAccount:
                    type: string
                  tolerations:
                    items:
                      properties:
                        effect:
                          type: string
                        key:
                          type: string
                        operator:
                          type: string
                        tolerationSeconds:
                          format: int64
                          type: integer
                        value:
                          type: string
                      type: object
                    type: array
                  topologySpreadConstraints:
                    items:
                      properties:
                        labelSelector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: string
                                  operator:
                                    type: string
                                  values:
                                    items:
                                      type: string
                                    type: array
                                    x-kubernetes-list-type: atomic
                                required:
                                - key
                                - operator
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            matchLabels:
                              additionalProperties:
                                type: string
                              type: object
                          type: object
                          x-kubernetes-map-type: atomic
                        matchLabelKeys:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        maxSkew:
                          format: int32
                          type: integer
                        minDomains:
                          format: int32
                          type: integer
                        nodeAffinityPolicy:
                          type: string
                        nodeTaintsPolicy:
                          type: string
                        topologyKey:
                          type: string
                        whenUnsatisfiable:
                          type: string
                      required:
                      - maxSkew
                      - topologyKey
                      - whenUnsatisfiable
                      type: object
                    type: array
                type: object
              terminationGracePeriodSeconds:
                format: int64
                type: integer
              tolerations:
                items:
                  properties:
                    effect:
                      type: string
                    key:
                      type: string
                    operator:
                      type: string
                    tolerationSeconds:
                      format: int64
                      type: integer
                    value:
                      type: string
                  type: object
                type: array
              topologySpreadConstraints:
                items:
                  properties:
                    labelSelector:
                      properties:
                        matchExpressions:
                          items:
                            properties:
                              key:
                                type: string
                              operator:
                                type: string
                              values:
                                items:
                                  type: string
                                type: array
                                x-kubernetes-list-type: atomic
                            required:
                            - key
                            - operator
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        matchLabels:
                          additionalProperties:
                            type: string
                          type: object
                      type: object
                      x-kubernetes-map-type: atomic
                    matchLabelKeys:
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    maxSkew:
                      format: int32
                      type: integer
                    minDomains:
                      format: int32
                      type: integer
                    nodeAffinityPolicy:
                      type: string
                    nodeTaintsPolicy:
                      type: string
                    topologyKey:
                      type: string
                    whenUnsatisfiable:
                      type: string
                  required:
                  - maxSkew
                  - topologyKey
                  - whenUnsatisfiable
                  type: object
                type: array
              updateStrategy:
                properties:
                  rollingUpdate:
                    properties:
                      maxSurge:
                        anyOf:
                        - type: integer
                        - type: string
                        x-kubernetes-int-or-string: true
                      maxUnavailable:
                        anyOf:
                        - type: integer
                        - type: string
                        x-kubernetes-int-or-string: true
                    type: object
                  type:
                    type: string
                type: object
              upgradeStrategy:
                enum:
                - automatic
                - none
                type: string
              volumeClaimTemplates:
                items:
                  properties:
                    apiVersion:
                      type: string
                    kind:
                      type: string
                    metadata:
                      properties:
                        annotations:
                          additionalProperties:
                            type: string
                          type: object
                        finalizers:
                          items:
                            type: string
                          type: array
                        labels:
                          additionalProperties:
                            type: string
                          type: object
                        name:
                          type: string
                        namespace:
                          type: string
                      type: object
                    spec:
                      properties:
                        accessModes:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        dataSource:
                          properties:
                            apiGroup:
                              type: string
                            kind:
                              type: string
                            name:
                              type: string
                          required:
                          - kind
                          - name
                          type: object
                          x-kubernetes-map-type: atomic
                        dataSourceRef:
                          properties:
                            apiGroup:
                              type: string
                            kind:
                              type: string
                            name:
                              type: string
                            namespace:
                              type: string
                          required:
                          - kind
                          - name
                          type: object
                        resources:
                          properties:
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              type: object
                          type: object
                        selector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: string
                                  operator:
                                    type: string
                                  values:
                                    items:
                                      type: string
                                    type: array
                                    x-kubernetes-list-type: atomic
                                required:
                                - key
                                - operator
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            matchLabels:
                              additionalProperties:
                                type: string
                              type: object
                          type: object
                          x-kubernetes-map-type: atomic
                        storageClassName:
                          type: string
                        volumeAttributesClassName:
                          type: string
                        volumeMode:
                          type: string
                        volumeName:
                          type: string
                      type: object
                    status:
                      properties:
                        accessModes:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        allocatedResourceStatuses:
                          additionalProperties:
                            type: string
                          type: object
                          x-kubernetes-map-type: granular
                        allocatedResources:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          type: object
                        capacity:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          type: object
                        conditions:
                          items:
                            properties:
                              lastProbeTime:
                                format: date-time
                                type: string
                              lastTransitionTime:
                                format: date-time
                                type: string
                              message:
                                type: string
                              reason:
                                type: string
                              status:
                                type: string
                              type:
                                type: string
                            required:
                            - status
                            - type
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - type
                          x-kubernetes-list-type: map
                        currentVolumeAttributesClassName:
                          type: string
                        modifyVolumeStatus:
                          properties:
                            status:
                              type: string
                            targetVolumeAttributesClassName:
                              type: string
                          required:
                          - status
                          type: object
                        phase:
                          type: string
                      type: object
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              volumeMounts:
                items:
                  properties:
                    mountPath:
                      type: string
                    mountPropagation:
                      type: string
                    name:
                      type: string
                    readOnly:
                      type: boolean
                    recursiveReadOnly:
                      type: string
                    subPath:
                      type: string
                    subPathExpr:
                      type: string
                  required:
                  - mountPath
                  - name
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              volumes:
                items:
                  properties:
                    awsElasticBlockStore:
                      properties:
                        fsType:
                          type: string
                        partition:
                          format: int32
                          type: integer
                        readOnly:
                          type: boolean
                        volumeID:
                          type: string
                      required:
                      - volumeID
                      type: object
                    azureDisk:
                      properties:
                        cachingMode:
                          type: string
                        diskName:
                          type: string
                        diskURI:
                          type: string
                        fsType:
                          default: ext4
                          type: string
                        kind:
                          type: string
                        readOnly:
                          default: false
                          type: boolean
                      required:
                      - diskName
                      - diskURI
                      type: object
                    azureFile:
                      properties:
                        readOnly:
                          type: boolean
                        secretName:
                          type: string
                        shareName:
                          type: string
                      required:
                      - secretName
                      - shareName
                      type: object
                    cephfs:
                      properties:
                        monitors:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        path:
                          type: string
                        readOnly:
                          type: boolean
                        secretFile:
                          type: string
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        user:
                          type: string
                      required:
                      - monitors
                      type: object
                    cinder:
                      properties:
                        fsType:
                          type: string
                        readOnly:
                          type: boolean
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        volumeID:
                          type: string
                      required:
                      - volumeID
                      type: object
                    configMap:
                      properties:
                        defaultMode:
                          format: int32
                          type: integer
                        items:
                          items:
                            properties:
                              key:
                                type: string
                              mode:
                                format: int32
                                type: integer
                              path:
                                type: string
                            required:
                            - key
                            - path
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        name:
                          default: ""
                          type: string
                        optional:
                          type: boolean
                      type: object
                      x-kubernetes-map-type: atomic
                    csi:
                      properties:
                        driver:
                          type: string
                        fsType:
                          type: string
                        nodePublishSecretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        readOnly:
                          type: boolean
                        volumeAttributes:
                          additionalProperties:
                            type: string
                          type: object
                      required:
                      - driver
                      type: object
                    downwardAPI:
                      properties:
                        defaultMode:
                          format: int32
                          type: integer
                        items:
                          items:
                            properties:
                              fieldRef:
                                properties:
                                  apiVersion:
                                    type: string
                                  fieldPath:
                                    type: string
                                required:
                                - fieldPath
                                type: object
                                x-kubernetes-map-type: atomic
                              mode:
                                format: int32
                                type: integer
                              path:
                                type: string
                              resourceFieldRef:
                                properties:
                                  containerName:
                                    type: string
                                  divisor:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  resource:
                                    type: string
                                required:
                                - resource
                                type: object
                                x-kubernetes-map-type: atomic
                            required:
                            - path
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    emptyDir:
                      properties:
                        medium:
                          type: string
                        sizeLimit:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                      type: object
                    ephemeral:
                      properties:
                        volumeClaimTemplate:
                          properties:
                            metadata:
                              properties:
                                annotations:
                                  additionalProperties:
                                    type: string
                                  type: object
                                finalizers:
                                  items:
                                    type: string
                                  type: array
                                labels:
                                  additionalProperties:
                                    type: string
                                  type: object
                                name:
                                  type: string
                                namespace:
                                  type: string
                              type: object
                            spec:
                              properties:
                                accessModes:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                dataSource:
                                  properties:
                                    apiGroup:
                                      type: string
                                    kind:
                                      type: string
                                    name:
                                      type: string
                                  required:
                                  - kind
                                  - name
                                  type: object
                                  x-kubernetes-map-type: atomic
                                dataSourceRef:
                                  properties:
                                    apiGroup:
                                      type: string
                                    kind:
                                      type: string
                                    name:
                                      type: string
                                    namespace:
                                      type: string
                                  required:
                                  - kind
                                  - name
                                  type: object
                                resources:
                                  properties:
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                selector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                storageClassName:
                                  type: string
                                volumeAttributesClassName:
                                  type: string
                                volumeMode:
                                  type: string
                                volumeName:
                                  type: string
                              type: object
                          required:
                          - spec
                          type: object
                      type: object
                    fc:
                      properties:
                        fsType:
                          type: string
                        lun:
                          format: int32
                          type: integer
                        readOnly:
                          type: boolean
                        targetWWNs:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        wwids:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    flexVolume:
                      properties:
                        driver:
                          type: string
                        fsType:
                          type: string
                        options:
                          additionalProperties:
                            type: string
                          type: object
                        readOnly:
                          type: boolean
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                      required:
                      - driver
                      type: object
                    flocker:
                      properties:
                        datasetName:
                          type: string
                        datasetUUID:
                          type: string
                      type: object
                    gcePersistentDisk:
                      properties:
                        fsType:
                          type: string
                        partition:
                          format: int32
                          type: integer
                        pdName:
                          type: string
                        readOnly:
                          type: boolean
                      required:
                      - pdName
                      type: object
                    gitRepo:
                      properties:
                        directory:
                          type: string
                        repository:
                          type: string
                        revision:
                          type: string
                      required:
                      - repository
                      type: object
                    glusterfs:
                      properties:
                        endpoints:
                          type: string
                        path:
                          type: string
                        readOnly:
                          type: boolean
                      required:
                      - endpoints
                      - path
                      type: object
                    hostPath:
                      properties:
                        path:
                          type: string
                        type:
                          type: string
                      required:
                      - path
                      type: object
                    image:
                      properties:
                        pullPolicy:
                          type: string
                        reference:
                          type: string
                      type: object
                    iscsi:
                      properties:
                        chapAuthDiscovery:
                          type: boolean
                        chapAuthSession:
                          type: boolean
                        fsType:
                          type: string
                        initiatorName:
                          type: string
                        iqn:
                          type: string
                        iscsiInterface:
                          default: default
                          type: string
                        lun:
                          format: int32
                          type: integer
                        portals:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        readOnly:
                          type: boolean
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        targetPortal:
                          type: string
                      required:
                      - iqn
                      - lun
                      - targetPortal
                      type: object
                    name:
                      type: string
                    nfs:
                      properties:
                        path:
                          type: string
                        readOnly:
                          type: boolean
                        server:
                          type: string
                      required:
                      - path
                      - server
                      type: object
                    persistentVolumeClaim:
                      properties:
                        claimName:
                          type: string
                        readOnly:
                          type: boolean
                      required:
                      - claimName
                      type: object
                    photonPersistentDisk:
                      properties:
                        fsType:
                          type: string
                        pdID:
                          type: string
                      required:
                      - pdID
                      type: object
                    portworxVolume:
                      properties:
                        fsType:
                          type: string
                        readOnly:
                          type: boolean
                        volumeID:
                          type: string
                      required:
                      - volumeID
                      type: object
                    projected:
                      properties:
                        defaultMode:
                          format: int32
                          type: integer
                        sources:
                          items:
                            properties:
                              clusterTrustBundle:
                                properties:
                                  labelSelector:
                                    properties:
                                      matchExpressions:
                                        items:
                                          properties:
                                            key:
                                              type: string
                                            operator:
                                              type: string
                                            values:
                                              items:
                                                type: string
                                              type: array
                                              x-kubernetes-list-type: atomic
                                          required:
                                          - key
                                          - operator
                                          type: object
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      matchLabels:
                                        additionalProperties:
                                          type: string
                                        type: object
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  name:
                                    type: string
                                  optional:
                                    type: boolean
                                  path:
                                    type: string
                                  signerName:
                                    type: string
                                required:
                                - path
                                type: object
                              configMap:
                                properties:
                                  items:
                                    items:
                                      properties:
                                        key:
                                          type: string
                                        mode:
                                          format: int32
                                          type: integer
                                        path:
                                          type: string
                                      required:
                                      - key
                                      - path
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  name:
                                    default: ""
                                    type: string
                                  optional:
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                              downwardAPI:
                                properties:
                                  items:
                                    items:
                                      properties:
                                        fieldRef:
                                          properties:
                                            apiVersion:
                                              type: string
                                            fieldPath:
                                              type: string
                                          required:
                                          - fieldPath
                                          type: object
                                          x-kubernetes-map-type: atomic
                                        mode:
                                          format: int32
                                          type: integer
                                        path:
                                          type: string
                                        resourceFieldRef:
                                          properties:
                                            containerName:
                                              type: string
                                            divisor:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                              x-kubernetes-int-or-string: true
                                            resource:
                                              type: string
                                          required:
                                          - resource
                                          type: object
                                          x-kubernetes-map-type: atomic
                                      required:
                                      - path
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                type: object
                              secret:
                                properties:
                                  items:
                                    items:
                                      properties:
                                        key:
                                          type: string
                                        mode:
                                          format: int32
                                          type: integer
                                        path:
                                          type: string
                                      required:
                                      - key
                                      - path
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  name:
                                    default: ""
                                    type: string
                                  optional:
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                              serviceAccountToken:
                                properties:
                                  audience:
                                    type: string
                                  expirationSeconds:
                                    format: int64
                                    type: integer
                                  path:
                                    type: string
                                required:
                                - path
                                type: object
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    quobyte:
                      properties:
                        group:
                          type: string
                        readOnly:
                          type: boolean
                        registry:
                          type: string
                        tenant:
                          type: string
                        user:
                          type: string
                        volume:
                          type: string
                      required:
                      - registry
                      - volume
                      type: object
                    rbd:
                      properties:
                        fsType:
                          type: string
                        image:
                          type: string
                        keyring:
                          default: /etc/ceph/keyring
                          type: string
                        monitors:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        pool:
                          default: rbd
                          type: string
                        readOnly:
                          type: boolean
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        user:
                          default: admin
                          type: string
                      required:
                      - image
                      - monitors
                      type: object
                    scaleIO:
                      properties:
                        fsType:
                          default: xfs
                          type: string
                        gateway:
                          type: string
                        protectionDomain:
                          type: string
                        readOnly:
                          type: boolean
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        sslEnabled:
                          type: boolean
                        storageMode:
                          default: ThinProvisioned
                          type: string
                        storagePool:
                          type: string
                        system:
                          type: string
                        volumeName:
                          type: string
                      required:
                      - gateway
                      - secretRef
                      - system
                      type: object
                    secret:
                      properties:
                        defaultMode:
                          format: int32
                          type: integer
                        items:
                          items:
                            properties:
                              key:
                                type: string
                              mode:
                                format: int32
                                type: integer
                              path:
                                type: string
                            required:
                            - key
                            - path
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        optional:
                          type: boolean
                        secretName:
                          type: string
                      type: object
                    storageos:
                      properties:
                        fsType:
                          type: string
                        readOnly:
                          type: boolean
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        volumeName:
                          type: string
                        volumeNamespace:
                          type: string
                      type: object
                    vsphereVolume:
                      properties:
                        fsType:
                          type: string
                        storagePolicyID:
                          type: string
                        storagePolicyName:
                          type: string
                        volumePath:
                          type: string
                      required:
                      - volumePath
                      type: object
                  required:
                  - name
                  type: object
                type: array
                x-kubernetes-list-type: atomic
            required:
            - config
            - managementState
            type: object
          status:
            properties:
              image:
                type: string
              messages:
                items:
                  type: string
                type: array
                x-kubernetes-list-type: atomic
              replicas:
                format: int32
                type: integer
              scale:
                properties:
                  replicas:
                    format: int32
                    type: integer
                  selector:
                    type: string
                  statusReplicas:
                    type: string
                type: object
              version:
                type: string
            type: object
        type: object
    served: true
    storage: false
    subresources:
      scale:
        labelSelectorPath: .status.scale.selector
        specReplicasPath: .spec.replicas
        statusReplicasPath: .status.scale.replicas
      status: {}
  - additionalPrinterColumns:
    - description: Deployment Mode
      jsonPath: .spec.mode
      name: Mode
      type: string
    - description: OpenTelemetry Version
      jsonPath: .status.version
      name: Version
      type: string
    - jsonPath: .status.scale.statusReplicas
      name: Ready
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .status.image
      name: Image
      type: string
    - description: Management State
      jsonPath: .spec.managementState
      name: Management
      type: string
    name: v1beta1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              additionalContainers:
                items:
                  properties:
                    args:
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    command:
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    env:
                      items:
                        properties:
                          name:
                            type: string
                          value:
                            type: string
                          valueFrom:
                            properties:
                              configMapKeyRef:
                                properties:
                                  key:
                                    type: string
                                  name:
                                    default: ""
                                    type: string
                                  optional:
                                    type: boolean
                                required:
                                - key
                                type: object
                                x-kubernetes-map-type: atomic
                              fieldRef:
                                properties:
                                  apiVersion:
                                    type: string
                                  fieldPath:
                                    type: string
                                required:
                                - fieldPath
                                type: object
                                x-kubernetes-map-type: atomic
                              resourceFieldRef:
                                properties:
                                  containerName:
                                    type: string
                                  divisor:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  resource:
                                    type: string
                                required:
                                - resource
                                type: object
                                x-kubernetes-map-type: atomic
                              secretKeyRef:
                                properties:
                                  key:
                                    type: string
                                  name:
                                    default: ""
                                    type: string
                                  optional:
                                    type: boolean
                                required:
                                - key
                                type: object
                                x-kubernetes-map-type: atomic
                            type: object
                        required:
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - name
                      x-kubernetes-list-type: map
                    envFrom:
                      items:
                        properties:
                          configMapRef:
                            properties:
                              name:
                                default: ""
                                type: string
                              optional:
                                type: boolean
                            type: object
                            x-kubernetes-map-type: atomic
                          prefix:
                            type: string
                          secretRef:
                            properties:
                              name:
                                default: ""
                                type: string
                              optional:
                                type: boolean
                            type: object
                            x-kubernetes-map-type: atomic
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    image:
                      type: string
                    imagePullPolicy:
                      type: string
                    lifecycle:
                      properties:
                        postStart:
                          properties:
                            exec:
                              properties:
                                command:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            httpGet:
                              properties:
                                host:
                                  type: string
                                httpHeaders:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                path:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                                scheme:
                                  type: string
                              required:
                              - port
                              type: object
                            sleep:
                              properties:
                                seconds:
                                  format: int64
                                  type: integer
                              required:
                              - seconds
                              type: object
                            tcpSocket:
                              properties:
                                host:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                              required:
                              - port
                              type: object
                          type: object
                        preStop:
                          properties:
                            exec:
                              properties:
                                command:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            httpGet:
                              properties:
                                host:
                                  type: string
                                httpHeaders:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                path:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                                scheme:
                                  type: string
                              required:
                              - port
                              type: object
                            sleep:
                              properties:
                                seconds:
                                  format: int64
                                  type: integer
                              required:
                              - seconds
                              type: object
                            tcpSocket:
                              properties:
                                host:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                              required:
                              - port
                              type: object
                          type: object
                      type: object
                    livenessProbe:
                      properties:
                        exec:
                          properties:
                            command:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          format: int32
                          type: integer
                        grpc:
                          properties:
                            port:
                              format: int32
                              type: integer
                            service:
                              default: ""
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          properties:
                            host:
                              type: string
                            httpHeaders:
                              items:
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            scheme:
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          format: int32
                          type: integer
                        periodSeconds:
                          format: int32
                          type: integer
                        successThreshold:
                          format: int32
                          type: integer
                        tcpSocket:
                          properties:
                            host:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          format: int64
                          type: integer
                        timeoutSeconds:
                          format: int32
                          type: integer
                      type: object
                    name:
                      type: string
                    ports:
                      items:
                        properties:
                          containerPort:
                            format: int32
                            type: integer
                          hostIP:
                            type: string
                          hostPort:
                            format: int32
                            type: integer
                          name:
                            type: string
                          protocol:
                            default: TCP
                            type: string
                        required:
                        - containerPort
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - containerPort
                      - protocol
                      x-kubernetes-list-type: map
                    readinessProbe:
                      properties:
                        exec:
                          properties:
                            command:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          format: int32
                          type: integer
                        grpc:
                          properties:
                            port:
                              format: int32
                              type: integer
                            service:
                              default: ""
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          properties:
                            host:
                              type: string
                            httpHeaders:
                              items:
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            scheme:
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          format: int32
                          type: integer
                        periodSeconds:
                          format: int32
                          type: integer
                        successThreshold:
                          format: int32
                          type: integer
                        tcpSocket:
                          properties:
                            host:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          format: int64
                          type: integer
                        timeoutSeconds:
                          format: int32
                          type: integer
                      type: object
                    resizePolicy:
                      items:
                        properties:
                          resourceName:
                            type: string
                          restartPolicy:
                            type: string
                        required:
                        - resourceName
                        - restartPolicy
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    resources:
                      properties:
                        claims:
                          items:
                            properties:
                              name:
                                type: string
                              request:
                                type: string
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        limits:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          type: object
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          type: object
                      type: object
                    restartPolicy:
                      type: string
                    securityContext:
                      properties:
                        allowPrivilegeEscalation:
                          type: boolean
                        appArmorProfile:
                          properties:
                            localhostProfile:
                              type: string
                            type:
                              type: string
                          required:
                          - type
                          type: object
                        capabilities:
                          properties:
                            add:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            drop:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        privileged:
                          type: boolean
                        procMount:
                          type: string
                        readOnlyRootFilesystem:
                          type: boolean
                        runAsGroup:
                          format: int64
                          type: integer
                        runAsNonRoot:
                          type: boolean
                        runAsUser:
                          format: int64
                          type: integer
                        seLinuxOptions:
                          properties:
                            level:
                              type: string
                            role:
                              type: string
                            type:
                              type: string
                            user:
                              type: string
                          type: object
                        seccompProfile:
                          properties:
                            localhostProfile:
                              type: string
                            type:
                              type: string
                          required:
                          - type
                          type: object
                        windowsOptions:
                          properties:
                            gmsaCredentialSpec:
                              type: string
                            gmsaCredentialSpecName:
                              type: string
                            hostProcess:
                              type: boolean
                            runAsUserName:
                              type: string
                          type: object
                      type: object
                    startupProbe:
                      properties:
                        exec:
                          properties:
                            command:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          format: int32
                          type: integer
                        grpc:
                          properties:
                            port:
                              format: int32
                              type: integer
                            service:
                              default: ""
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          properties:
                            host:
                              type: string
                            httpHeaders:
                              items:
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            scheme:
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          format: int32
                          type: integer
                        periodSeconds:
                          format: int32
                          type: integer
                        successThreshold:
                          format: int32
                          type: integer
                        tcpSocket:
                          properties:
                            host:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          format: int64
                          type: integer
                        timeoutSeconds:
                          format: int32
                          type: integer
                      type: object
                    stdin:
                      type: boolean
                    stdinOnce:
                      type: boolean
                    terminationMessagePath:
                      type: string
                    terminationMessagePolicy:
                      type: string
                    tty:
                      type: boolean
                    volumeDevices:
                      items:
                        properties:
                          devicePath:
                            type: string
                          name:
                            type: string
                        required:
                        - devicePath
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - devicePath
                      x-kubernetes-list-type: map
                    volumeMounts:
                      items:
                        properties:
                          mountPath:
                            type: string
                          mountPropagation:
                            type: string
                          name:
                            type: string
                          readOnly:
                            type: boolean
                          recursiveReadOnly:
                            type: string
                          subPath:
                            type: string
                          subPathExpr:
                            type: string
                        required:
                        - mountPath
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - mountPath
                      x-kubernetes-list-type: map
                    workingDir:
                      type: string
                  required:
                  - name
                  type: object
                type: array
              affinity:
                properties:
                  nodeAffinity:
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        items:
                          properties:
                            preference:
                              properties:
                                matchExpressions:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchFields:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                              x-kubernetes-map-type: atomic
                            weight:
                              format: int32
                              type: integer
                          required:
                          - preference
                          - weight
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      requiredDuringSchedulingIgnoredDuringExecution:
                        properties:
                          nodeSelectorTerms:
                            items:
                              properties:
                                matchExpressions:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchFields:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                              x-kubernetes-map-type: atomic
                            type: array
                            x-kubernetes-list-type: atomic
                        required:
                        - nodeSelectorTerms
                        type: object
                        x-kubernetes-map-type: atomic
                    type: object
                  podAffinity:
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        items:
                          properties:
                            podAffinityTerm:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            weight:
                              format: int32
                              type: integer
                          required:
                          - podAffinityTerm
                          - weight
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      requiredDuringSchedulingIgnoredDuringExecution:
                        items:
                          properties:
                            labelSelector:
                              properties:
                                matchExpressions:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            matchLabelKeys:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            mismatchLabelKeys:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            namespaceSelector:
                              properties:
                                matchExpressions:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            topologyKey:
                              type: string
                          required:
                          - topologyKey
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  podAntiAffinity:
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        items:
                          properties:
                            podAffinityTerm:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            weight:
                              format: int32
                              type: integer
                          required:
                          - podAffinityTerm
                          - weight
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      requiredDuringSchedulingIgnoredDuringExecution:
                        items:
                          properties:
                            labelSelector:
                              properties:
                                matchExpressions:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            matchLabelKeys:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            mismatchLabelKeys:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            namespaceSelector:
                              properties:
                                matchExpressions:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      operator:
                                        type: string
                                      values:
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            topologyKey:
                              type: string
                          required:
                          - topologyKey
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                type: object
              args:
                additionalProperties:
                  type: string
                type: object
              autoscaler:
                properties:
                  behavior:
                    properties:
                      scaleDown:
                        properties:
                          policies:
                            items:
                              properties:
                                periodSeconds:
                                  format: int32
                                  type: integer
                                type:
                                  type: string
                                value:
                                  format: int32
                                  type: integer
                              required:
                              - periodSeconds
                              - type
                              - value
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          selectPolicy:
                            type: string
                          stabilizationWindowSeconds:
                            format: int32
                            type: integer
                        type: object
                      scaleUp:
                        properties:
                          policies:
                            items:
                              properties:
                                periodSeconds:
                                  format: int32
                                  type: integer
                                type:
                                  type: string
                                value:
                                  format: int32
                                  type: integer
                              required:
                              - periodSeconds
                              - type
                              - value
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          selectPolicy:
                            type: string
                          stabilizationWindowSeconds:
                            format: int32
                            type: integer
                        type: object
                    type: object
                  maxReplicas:
                    format: int32
                    type: integer
                  metrics:
                    items:
                      properties:
                        pods:
                          properties:
                            metric:
                              properties:
                                name:
                                  type: string
                                selector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                              required:
                              - name
                              type: object
                            target:
                              properties:
                                averageUtilization:
                                  format: int32
                                  type: integer
                                averageValue:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                type:
                                  type: string
                                value:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                              required:
                              - type
                              type: object
                          required:
                          - metric
                          - target
                          type: object
                        type:
                          type: string
                      required:
                      - type
                      type: object
                    type: array
                  minReplicas:
                    format: int32
                    type: integer
                  targetCPUUtilization:
                    format: int32
                    type: integer
                  targetMemoryUtilization:
                    format: int32
                    type: integer
                type: object
              config:
                properties:
                  connectors:
                    type: object
                    x-kubernetes-preserve-unknown-fields: true
                  exporters:
                    type: object
                    x-kubernetes-preserve-unknown-fields: true
                  extensions:
                    type: object
                    x-kubernetes-preserve-unknown-fields: true
                  processors:
                    type: object
                    x-kubernetes-preserve-unknown-fields: true
                  receivers:
                    type: object
                    x-kubernetes-preserve-unknown-fields: true
                  service:
                    properties:
                      extensions:
                        items:
                          type: string
                        type: array
                      pipelines:
                        additionalProperties:
                          properties:
                            exporters:
                              items:
                                type: string
                              type: array
                            processors:
                              items:
                                type: string
                              type: array
                            receivers:
                              items:
                                type: string
                              type: array
                          required:
                          - exporters
                          - receivers
                          type: object
                        type: object
                        x-kubernetes-preserve-unknown-fields: true
                      telemetry:
                        type: object
                        x-kubernetes-preserve-unknown-fields: true
                    required:
                    - pipelines
                    type: object
                required:
                - exporters
                - receivers
                - service
                type: object
                x-kubernetes-preserve-unknown-fields: true
              configVersions:
                default: 3
                minimum: 1
                type: integer
              configmaps:
                items:
                  properties:
                    mountpath:
                      type: string
                    name:
                      type: string
                  required:
                  - mountpath
                  - name
                  type: object
                type: array
              daemonSetUpdateStrategy:
                properties:
                  rollingUpdate:
                    properties:
                      maxSurge:
                        anyOf:
                        - type: integer
                        - type: string
                        x-kubernetes-int-or-string: true
                      maxUnavailable:
                        anyOf:
                        - type: integer
                        - type: string
                        x-kubernetes-int-or-string: true
                    type: object
                  type:
                    type: string
                type: object
              deploymentUpdateStrategy:
                properties:
                  rollingUpdate:
                    properties:
                      maxSurge:
                        anyOf:
                        - type: integer
                        - type: string
                        x-kubernetes-int-or-string: true
                      maxUnavailable:
                        anyOf:
                        - type: integer
                        - type: string
                        x-kubernetes-int-or-string: true
                    type: object
                  type:
                    type: string
                type: object
              env:
                items:
                  properties:
                    name:
                      type: string
                    value:
                      type: string
                    valueFrom:
                      properties:
                        configMapKeyRef:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        fieldRef:
                          properties:
                            apiVersion:
                              type: string
                            fieldPath:
                              type: string
                          required:
                          - fieldPath
                          type: object
                          x-kubernetes-map-type: atomic
                        resourceFieldRef:
                          properties:
                            containerName:
                              type: string
                            divisor:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            resource:
                              type: string
                          required:
                          - resource
                          type: object
                          x-kubernetes-map-type: atomic
                        secretKeyRef:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                      type: object
                  required:
                  - name
                  type: object
                type: array
              envFrom:
                items:
                  properties:
                    configMapRef:
                      properties:
                        name:
                          default: ""
                          type: string
                        optional:
                          type: boolean
                      type: object
                      x-kubernetes-map-type: atomic
                    prefix:
                      type: string
                    secretRef:
                      properties:
                        name:
                          default: ""
                          type: string
                        optional:
                          type: boolean
                      type: object
                      x-kubernetes-map-type: atomic
                  type: object
                type: array
              hostNetwork:
                type: boolean
              image:
                type: string
              imagePullPolicy:
                type: string
              ingress:
                properties:
                  annotations:
                    additionalProperties:
                      type: string
                    type: object
                  hostname:
                    type: string
                  ingressClassName:
                    type: string
                  route:
                    properties:
                      termination:
                        enum:
                        - insecure
                        - edge
                        - passthrough
                        - reencrypt
                        type: string
                    type: object
                  ruleType:
                    enum:
                    - path
                    - subdomain
                    type: string
                  tls:
                    items:
                      properties:
                        hosts:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        secretName:
                          type: string
                      type: object
                    type: array
                  type:
                    enum:
                    - ingress
                    - route
                    type: string
                type: object
              initContainers:
                items:
                  properties:
                    args:
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    command:
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    env:
                      items:
                        properties:
                          name:
                            type: string
                          value:
                            type: string
                          valueFrom:
                            properties:
                              configMapKeyRef:
                                properties:
                                  key:
                                    type: string
                                  name:
                                    default: ""
                                    type: string
                                  optional:
                                    type: boolean
                                required:
                                - key
                                type: object
                                x-kubernetes-map-type: atomic
                              fieldRef:
                                properties:
                                  apiVersion:
                                    type: string
                                  fieldPath:
                                    type: string
                                required:
                                - fieldPath
                                type: object
                                x-kubernetes-map-type: atomic
                              resourceFieldRef:
                                properties:
                                  containerName:
                                    type: string
                                  divisor:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  resource:
                                    type: string
                                required:
                                - resource
                                type: object
                                x-kubernetes-map-type: atomic
                              secretKeyRef:
                                properties:
                                  key:
                                    type: string
                                  name:
                                    default: ""
                                    type: string
                                  optional:
                                    type: boolean
                                required:
                                - key
                                type: object
                                x-kubernetes-map-type: atomic
                            type: object
                        required:
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - name
                      x-kubernetes-list-type: map
                    envFrom:
                      items:
                        properties:
                          configMapRef:
                            properties:
                              name:
                                default: ""
                                type: string
                              optional:
                                type: boolean
                            type: object
                            x-kubernetes-map-type: atomic
                          prefix:
                            type: string
                          secretRef:
                            properties:
                              name:
                                default: ""
                                type: string
                              optional:
                                type: boolean
                            type: object
                            x-kubernetes-map-type: atomic
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    image:
                      type: string
                    imagePullPolicy:
                      type: string
                    lifecycle:
                      properties:
                        postStart:
                          properties:
                            exec:
                              properties:
                                command:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            httpGet:
                              properties:
                                host:
                                  type: string
                                httpHeaders:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                path:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                                scheme:
                                  type: string
                              required:
                              - port
                              type: object
                            sleep:
                              properties:
                                seconds:
                                  format: int64
                                  type: integer
                              required:
                              - seconds
                              type: object
                            tcpSocket:
                              properties:
                                host:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                              required:
                              - port
                              type: object
                          type: object
                        preStop:
                          properties:
                            exec:
                              properties:
                                command:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            httpGet:
                              properties:
                                host:
                                  type: string
                                httpHeaders:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                path:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                                scheme:
                                  type: string
                              required:
                              - port
                              type: object
                            sleep:
                              properties:
                                seconds:
                                  format: int64
                                  type: integer
                              required:
                              - seconds
                              type: object
                            tcpSocket:
                              properties:
                                host:
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                              required:
                              - port
                              type: object
                          type: object
                      type: object
                    livenessProbe:
                      properties:
                        exec:
                          properties:
                            command:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          format: int32
                          type: integer
                        grpc:
                          properties:
                            port:
                              format: int32
                              type: integer
                            service:
                              default: ""
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          properties:
                            host:
                              type: string
                            httpHeaders:
                              items:
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            scheme:
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          format: int32
                          type: integer
                        periodSeconds:
                          format: int32
                          type: integer
                        successThreshold:
                          format: int32
                          type: integer
                        tcpSocket:
                          properties:
                            host:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          format: int64
                          type: integer
                        timeoutSeconds:
                          format: int32
                          type: integer
                      type: object
                    name:
                      type: string
                    ports:
                      items:
                        properties:
                          containerPort:
                            format: int32
                            type: integer
                          hostIP:
                            type: string
                          hostPort:
                            format: int32
                            type: integer
                          name:
                            type: string
                          protocol:
                            default: TCP
                            type: string
                        required:
                        - containerPort
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - containerPort
                      - protocol
                      x-kubernetes-list-type: map
                    readinessProbe:
                      properties:
                        exec:
                          properties:
                            command:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          format: int32
                          type: integer
                        grpc:
                          properties:
                            port:
                              format: int32
                              type: integer
                            service:
                              default: ""
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          properties:
                            host:
                              type: string
                            httpHeaders:
                              items:
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            scheme:
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          format: int32
                          type: integer
                        periodSeconds:
                          format: int32
                          type: integer
                        successThreshold:
                          format: int32
                          type: integer
                        tcpSocket:
                          properties:
                            host:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          format: int64
                          type: integer
                        timeoutSeconds:
                          format: int32
                          type: integer
                      type: object
                    resizePolicy:
                      items:
                        properties:
                          resourceName:
                            type: string
                          restartPolicy:
                            type: string
                        required:
                        - resourceName
                        - restartPolicy
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    resources:
                      properties:
                        claims:
                          items:
                            properties:
                              name:
                                type: string
                              request:
                                type: string
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        limits:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          type: object
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          type: object
                      type: object
                    restartPolicy:
                      type: string
                    securityContext:
                      properties:
                        allowPrivilegeEscalation:
                          type: boolean
                        appArmorProfile:
                          properties:
                            localhostProfile:
                              type: string
                            type:
                              type: string
                          required:
                          - type
                          type: object
                        capabilities:
                          properties:
                            add:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            drop:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        privileged:
                          type: boolean
                        procMount:
                          type: string
                        readOnlyRootFilesystem:
                          type: boolean
                        runAsGroup:
                          format: int64
                          type: integer
                        runAsNonRoot:
                          type: boolean
                        runAsUser:
                          format: int64
                          type: integer
                        seLinuxOptions:
                          properties:
                            level:
                              type: string
                            role:
                              type: string
                            type:
                              type: string
                            user:
                              type: string
                          type: object
                        seccompProfile:
                          properties:
                            localhostProfile:
                              type: string
                            type:
                              type: string
                          required:
                          - type
                          type: object
                        windowsOptions:
                          properties:
                            gmsaCredentialSpec:
                              type: string
                            gmsaCredentialSpecName:
                              type: string
                            hostProcess:
                              type: boolean
                            runAsUserName:
                              type: string
                          type: object
                      type: object
                    startupProbe:
                      properties:
                        exec:
                          properties:
                            command:
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          format: int32
                          type: integer
                        grpc:
                          properties:
                            port:
                              format: int32
                              type: integer
                            service:
                              default: ""
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          properties:
                            host:
                              type: string
                            httpHeaders:
                              items:
                                properties:
                                  name:
                                    type: string
                                  value:
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            scheme:
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          format: int32
                          type: integer
                        periodSeconds:
                          format: int32
                          type: integer
                        successThreshold:
                          format: int32
                          type: integer
                        tcpSocket:
                          properties:
                            host:
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          format: int64
                          type: integer
                        timeoutSeconds:
                          format: int32
                          type: integer
                      type: object
                    stdin:
                      type: boolean
                    stdinOnce:
                      type: boolean
                    terminationMessagePath:
                      type: string
                    terminationMessagePolicy:
                      type: string
                    tty:
                      type: boolean
                    volumeDevices:
                      items:
                        properties:
                          devicePath:
                            type: string
                          name:
                            type: string
                        required:
                        - devicePath
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - devicePath
                      x-kubernetes-list-type: map
                    volumeMounts:
                      items:
                        properties:
                          mountPath:
                            type: string
                          mountPropagation:
                            type: string
                          name:
                            type: string
                          readOnly:
                            type: boolean
                          recursiveReadOnly:
                            type: string
                          subPath:
                            type: string
                          subPathExpr:
                            type: string
                        required:
                        - mountPath
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - mountPath
                      x-kubernetes-list-type: map
                    workingDir:
                      type: string
                  required:
                  - name
                  type: object
                type: array
              ipFamilies:
                items:
                  type: string
                type: array
              ipFamilyPolicy:
                default: SingleStack
                type: string
              lifecycle:
                properties:
                  postStart:
                    properties:
                      exec:
                        properties:
                          command:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      httpGet:
                        properties:
                          host:
                            type: string
                          httpHeaders:
                            items:
                              properties:
                                name:
                                  type: string
                                value:
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          path:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                          scheme:
                            type: string
                        required:
                        - port
                        type: object
                      sleep:
                        properties:
                          seconds:
                            format: int64
                            type: integer
                        required:
                        - seconds
                        type: object
                      tcpSocket:
                        properties:
                          host:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                        required:
                        - port
                        type: object
                    type: object
                  preStop:
                    properties:
                      exec:
                        properties:
                          command:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      httpGet:
                        properties:
                          host:
                            type: string
                          httpHeaders:
                            items:
                              properties:
                                name:
                                  type: string
                                value:
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          path:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                          scheme:
                            type: string
                        required:
                        - port
                        type: object
                      sleep:
                        properties:
                          seconds:
                            format: int64
                            type: integer
                        required:
                        - seconds
                        type: object
                      tcpSocket:
                        properties:
                          host:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                        required:
                        - port
                        type: object
                    type: object
                type: object
              livenessProbe:
                properties:
                  failureThreshold:
                    format: int32
                    type: integer
                  initialDelaySeconds:
                    format: int32
                    type: integer
                  periodSeconds:
                    format: int32
                    type: integer
                  successThreshold:
                    format: int32
                    type: integer
                  terminationGracePeriodSeconds:
                    format: int64
                    type: integer
                  timeoutSeconds:
                    format: int32
                    type: integer
                type: object
              managementState:
                default: managed
                enum:
                - managed
                - unmanaged
                type: string
              mode:
                enum:
                - daemonset
                - deployment
                - sidecar
                - statefulset
                type: string
              nodeSelector:
                additionalProperties:
                  type: string
                type: object
              observability:
                properties:
                  metrics:
                    properties:
                      disablePrometheusAnnotations:
                        type: boolean
                      enableMetrics:
                        type: boolean
                    type: object
                type: object
              persistentVolumeClaimRetentionPolicy:
                properties:
                  whenDeleted:
                    type: string
                  whenScaled:
                    type: string
                type: object
              podAnnotations:
                additionalProperties:
                  type: string
                type: object
              podDisruptionBudget:
                properties:
                  maxUnavailable:
                    anyOf:
                    - type: integer
                    - type: string
                    x-kubernetes-int-or-string: true
                  minAvailable:
                    anyOf:
                    - type: integer
                    - type: string
                    x-kubernetes-int-or-string: true
                type: object
              podDnsConfig:
                properties:
                  nameservers:
                    items:
                      type: string
                    type: array
                    x-kubernetes-list-type: atomic
                  options:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  searches:
                    items:
                      type: string
                    type: array
                    x-kubernetes-list-type: atomic
                type: object
              podSecurityContext:
                properties:
                  appArmorProfile:
                    properties:
                      localhostProfile:
                        type: string
                      type:
                        type: string
                    required:
                    - type
                    type: object
                  fsGroup:
                    format: int64
                    type: integer
                  fsGroupChangePolicy:
                    type: string
                  runAsGroup:
                    format: int64
                    type: integer
                  runAsNonRoot:
                    type: boolean
                  runAsUser:
                    format: int64
                    type: integer
                  seLinuxChangePolicy:
                    type: string
                  seLinuxOptions:
                    properties:
                      level:
                        type: string
                      role:
                        type: string
                      type:
                        type: string
                      user:
                        type: string
                    type: object
                  seccompProfile:
                    properties:
                      localhostProfile:
                        type: string
                      type:
                        type: string
                    required:
                    - type
                    type: object
                  supplementalGroups:
                    items:
                      format: int64
                      type: integer
                    type: array
                    x-kubernetes-list-type: atomic
                  supplementalGroupsPolicy:
                    type: string
                  sysctls:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                      required:
                      - name
                      - value
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  windowsOptions:
                    properties:
                      gmsaCredentialSpec:
                        type: string
                      gmsaCredentialSpecName:
                        type: string
                      hostProcess:
                        type: boolean
                      runAsUserName:
                        type: string
                    type: object
                type: object
              ports:
                items:
                  properties:
                    appProtocol:
                      type: string
                    hostPort:
                      format: int32
                      type: integer
                    name:
                      type: string
                    nodePort:
                      format: int32
                      type: integer
                    port:
                      format: int32
                      type: integer
                    protocol:
                      default: TCP
                      type: string
                    targetPort:
                      anyOf:
                      - type: integer
                      - type: string
                      x-kubernetes-int-or-string: true
                  required:
                  - port
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              priorityClassName:
                type: string
              readinessProbe:
                properties:
                  failureThreshold:
                    format: int32
                    type: integer
                  initialDelaySeconds:
                    format: int32
                    type: integer
                  periodSeconds:
                    format: int32
                    type: integer
                  successThreshold:
                    format: int32
                    type: integer
                  terminationGracePeriodSeconds:
                    format: int64
                    type: integer
                  timeoutSeconds:
                    format: int32
                    type: integer
                type: object
              replicas:
                default: 1
                format: int32
                type: integer
              resources:
                properties:
                  claims:
                    items:
                      properties:
                        name:
                          type: string
                        request:
                          type: string
                      required:
                      - name
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - name
                    x-kubernetes-list-type: map
                  limits:
                    additionalProperties:
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                      x-kubernetes-int-or-string: true
                    type: object
                  requests:
                    additionalProperties:
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                      x-kubernetes-int-or-string: true
                    type: object
                type: object
              securityContext:
                properties:
                  allowPrivilegeEscalation:
                    type: boolean
                  appArmorProfile:
                    properties:
                      localhostProfile:
                        type: string
                      type:
                        type: string
                    required:
                    - type
                    type: object
                  capabilities:
                    properties:
                      add:
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                      drop:
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  privileged:
                    type: boolean
                  procMount:
                    type: string
                  readOnlyRootFilesystem:
                    type: boolean
                  runAsGroup:
                    format: int64
                    type: integer
                  runAsNonRoot:
                    type: boolean
                  runAsUser:
                    format: int64
                    type: integer
                  seLinuxOptions:
                    properties:
                      level:
                        type: string
                      role:
                        type: string
                      type:
                        type: string
                      user:
                        type: string
                    type: object
                  seccompProfile:
                    properties:
                      localhostProfile:
                        type: string
                      type:
                        type: string
                    required:
                    - type
                    type: object
                  windowsOptions:
                    properties:
                      gmsaCredentialSpec:
                        type: string
                      gmsaCredentialSpecName:
                        type: string
                      hostProcess:
                        type: boolean
                      runAsUserName:
                        type: string
                    type: object
                type: object
              serviceAccount:
                type: string
              serviceName:
                type: string
              shareProcessNamespace:
                type: boolean
              targetAllocator:
                properties:
                  affinity:
                    properties:
                      nodeAffinity:
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                preference:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchFields:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  type: object
                                  x-kubernetes-map-type: atomic
                                weight:
                                  format: int32
                                  type: integer
                              required:
                              - preference
                              - weight
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          requiredDuringSchedulingIgnoredDuringExecution:
                            properties:
                              nodeSelectorTerms:
                                items:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchFields:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  type: object
                                  x-kubernetes-map-type: atomic
                                type: array
                                x-kubernetes-list-type: atomic
                            required:
                            - nodeSelectorTerms
                            type: object
                            x-kubernetes-map-type: atomic
                        type: object
                      podAffinity:
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                podAffinityTerm:
                                  properties:
                                    labelSelector:
                                      properties:
                                        matchExpressions:
                                          items:
                                            properties:
                                              key:
                                                type: string
                                              operator:
                                                type: string
                                              values:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    matchLabelKeys:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    mismatchLabelKeys:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    namespaceSelector:
                                      properties:
                                        matchExpressions:
                                          items:
                                            properties:
                                              key:
                                                type: string
                                              operator:
                                                type: string
                                              values:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    namespaces:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    topologyKey:
                                      type: string
                                  required:
                                  - topologyKey
                                  type: object
                                weight:
                                  format: int32
                                  type: integer
                              required:
                              - podAffinityTerm
                              - weight
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          requiredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      podAntiAffinity:
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                podAffinityTerm:
                                  properties:
                                    labelSelector:
                                      properties:
                                        matchExpressions:
                                          items:
                                            properties:
                                              key:
                                                type: string
                                              operator:
                                                type: string
                                              values:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    matchLabelKeys:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    mismatchLabelKeys:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    namespaceSelector:
                                      properties:
                                        matchExpressions:
                                          items:
                                            properties:
                                              key:
                                                type: string
                                              operator:
                                                type: string
                                              values:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    namespaces:
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    topologyKey:
                                      type: string
                                  required:
                                  - topologyKey
                                  type: object
                                weight:
                                  format: int32
                                  type: integer
                              required:
                              - podAffinityTerm
                              - weight
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          requiredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                    type: object
                  allocationStrategy:
                    default: consistent-hashing
                    enum:
                    - least-weighted
                    - consistent-hashing
                    - per-node
                    type: string
                  collectorNotReadyGracePeriod:
                    default: 30s
                    format: duration
                    type: string
                  enabled:
                    type: boolean
                  env:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  filterStrategy:
                    default: relabel-config
                    enum:
                    - ""
                    - relabel-config
                    type: string
                  image:
                    type: string
                  nodeSelector:
                    additionalProperties:
                      type: string
                    type: object
                  observability:
                    properties:
                      metrics:
                        properties:
                          disablePrometheusAnnotations:
                            type: boolean
                          enableMetrics:
                            type: boolean
                        type: object
                    type: object
                  podDisruptionBudget:
                    properties:
                      maxUnavailable:
                        anyOf:
                        - type: integer
                        - type: string
                        x-kubernetes-int-or-string: true
                      minAvailable:
                        anyOf:
                        - type: integer
                        - type: string
                        x-kubernetes-int-or-string: true
                    type: object
                  podSecurityContext:
                    properties:
                      appArmorProfile:
                        properties:
                          localhostProfile:
                            type: string
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      fsGroup:
                        format: int64
                        type: integer
                      fsGroupChangePolicy:
                        type: string
                      runAsGroup:
                        format: int64
                        type: integer
                      runAsNonRoot:
                        type: boolean
                      runAsUser:
                        format: int64
                        type: integer
                      seLinuxChangePolicy:
                        type: string
                      seLinuxOptions:
                        properties:
                          level:
                            type: string
                          role:
                            type: string
                          type:
                            type: string
                          user:
                            type: string
                        type: object
                      seccompProfile:
                        properties:
                          localhostProfile:
                            type: string
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      supplementalGroups:
                        items:
                          format: int64
                          type: integer
                        type: array
                        x-kubernetes-list-type: atomic
                      supplementalGroupsPolicy:
                        type: string
                      sysctls:
                        items:
                          properties:
                            name:
                              type: string
                            value:
                              type: string
                          required:
                          - name
                          - value
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      windowsOptions:
                        properties:
                          gmsaCredentialSpec:
                            type: string
                          gmsaCredentialSpecName:
                            type: string
                          hostProcess:
                            type: boolean
                          runAsUserName:
                            type: string
                        type: object
                    type: object
                  prometheusCR:
                    properties:
                      allowNamespaces:
                        items:
                          type: string
                        type: array
                      denyNamespaces:
                        items:
                          type: string
                        type: array
                      enabled:
                        type: boolean
                      podMonitorSelector:
                        properties:
                          matchExpressions:
                            items:
                              properties:
                                key:
                                  type: string
                                operator:
                                  type: string
                                values:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              required:
                              - key
                              - operator
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          matchLabels:
                            additionalProperties:
                              type: string
                            type: object
                        type: object
                        x-kubernetes-map-type: atomic
                      probeSelector:
                        properties:
                          matchExpressions:
                            items:
                              properties:
                                key:
                                  type: string
                                operator:
                                  type: string
                                values:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              required:
                              - key
                              - operator
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          matchLabels:
                            additionalProperties:
                              type: string
                            type: object
                        type: object
                        x-kubernetes-map-type: atomic
                      scrapeConfigSelector:
                        properties:
                          matchExpressions:
                            items:
                              properties:
                                key:
                                  type: string
                                operator:
                                  type: string
                                values:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              required:
                              - key
                              - operator
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          matchLabels:
                            additionalProperties:
                              type: string
                            type: object
                        type: object
                        x-kubernetes-map-type: atomic
                      scrapeInterval:
                        default: 30s
                        format: duration
                        type: string
                      serviceMonitorSelector:
                        properties:
                          matchExpressions:
                            items:
                              properties:
                                key:
                                  type: string
                                operator:
                                  type: string
                                values:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              required:
                              - key
                              - operator
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          matchLabels:
                            additionalProperties:
                              type: string
                            type: object
                        type: object
                        x-kubernetes-map-type: atomic
                    type: object
                  replicas:
                    format: int32
                    type: integer
                  resources:
                    properties:
                      claims:
                        items:
                          properties:
                            name:
                              type: string
                            request:
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                    type: object
                  securityContext:
                    properties:
                      allowPrivilegeEscalation:
                        type: boolean
                      appArmorProfile:
                        properties:
                          localhostProfile:
                            type: string
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      capabilities:
                        properties:
                          add:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          drop:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      privileged:
                        type: boolean
                      procMount:
                        type: string
                      readOnlyRootFilesystem:
                        type: boolean
                      runAsGroup:
                        format: int64
                        type: integer
                      runAsNonRoot:
                        type: boolean
                      runAsUser:
                        format: int64
                        type: integer
                      seLinuxOptions:
                        properties:
                          level:
                            type: string
                          role:
                            type: string
                          type:
                            type: string
                          user:
                            type: string
                        type: object
                      seccompProfile:
                        properties:
                          localhostProfile:
                            type: string
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      windowsOptions:
                        properties:
                          gmsaCredentialSpec:
                            type: string
                          gmsaCredentialSpecName:
                            type: string
                          hostProcess:
                            type: boolean
                          runAsUserName:
                            type: string
                        type: object
                    type: object
                  serviceAccount:
                    type: string
                  tolerations:
                    items:
                      properties:
                        effect:
                          type: string
                        key:
                          type: string
                        operator:
                          type: string
                        tolerationSeconds:
                          format: int64
                          type: integer
                        value:
                          type: string
                      type: object
                    type: array
                  topologySpreadConstraints:
                    items:
                      properties:
                        labelSelector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: string
                                  operator:
                                    type: string
                                  values:
                                    items:
                                      type: string
                                    type: array
                                    x-kubernetes-list-type: atomic
                                required:
                                - key
                                - operator
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            matchLabels:
                              additionalProperties:
                                type: string
                              type: object
                          type: object
                          x-kubernetes-map-type: atomic
                        matchLabelKeys:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        maxSkew:
                          format: int32
                          type: integer
                        minDomains:
                          format: int32
                          type: integer
                        nodeAffinityPolicy:
                          type: string
                        nodeTaintsPolicy:
                          type: string
                        topologyKey:
                          type: string
                        whenUnsatisfiable:
                          type: string
                      required:
                      - maxSkew
                      - topologyKey
                      - whenUnsatisfiable
                      type: object
                    type: array
                type: object
              terminationGracePeriodSeconds:
                format: int64
                type: integer
              tolerations:
                items:
                  properties:
                    effect:
                      type: string
                    key:
                      type: string
                    operator:
                      type: string
                    tolerationSeconds:
                      format: int64
                      type: integer
                    value:
                      type: string
                  type: object
                type: array
              topologySpreadConstraints:
                items:
                  properties:
                    labelSelector:
                      properties:
                        matchExpressions:
                          items:
                            properties:
                              key:
                                type: string
                              operator:
                                type: string
                              values:
                                items:
                                  type: string
                                type: array
                                x-kubernetes-list-type: atomic
                            required:
                            - key
                            - operator
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        matchLabels:
                          additionalProperties:
                            type: string
                          type: object
                      type: object
                      x-kubernetes-map-type: atomic
                    matchLabelKeys:
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    maxSkew:
                      format: int32
                      type: integer
                    minDomains:
                      format: int32
                      type: integer
                    nodeAffinityPolicy:
                      type: string
                    nodeTaintsPolicy:
                      type: string
                    topologyKey:
                      type: string
                    whenUnsatisfiable:
                      type: string
                  required:
                  - maxSkew
                  - topologyKey
                  - whenUnsatisfiable
                  type: object
                type: array
              upgradeStrategy:
                enum:
                - automatic
                - none
                type: string
              volumeClaimTemplates:
                items:
                  properties:
                    apiVersion:
                      type: string
                    kind:
                      type: string
                    metadata:
                      properties:
                        annotations:
                          additionalProperties:
                            type: string
                          type: object
                        finalizers:
                          items:
                            type: string
                          type: array
                        labels:
                          additionalProperties:
                            type: string
                          type: object
                        name:
                          type: string
                        namespace:
                          type: string
                      type: object
                    spec:
                      properties:
                        accessModes:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        dataSource:
                          properties:
                            apiGroup:
                              type: string
                            kind:
                              type: string
                            name:
                              type: string
                          required:
                          - kind
                          - name
                          type: object
                          x-kubernetes-map-type: atomic
                        dataSourceRef:
                          properties:
                            apiGroup:
                              type: string
                            kind:
                              type: string
                            name:
                              type: string
                            namespace:
                              type: string
                          required:
                          - kind
                          - name
                          type: object
                        resources:
                          properties:
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              type: object
                          type: object
                        selector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: string
                                  operator:
                                    type: string
                                  values:
                                    items:
                                      type: string
                                    type: array
                                    x-kubernetes-list-type: atomic
                                required:
                                - key
                                - operator
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            matchLabels:
                              additionalProperties:
                                type: string
                              type: object
                          type: object
                          x-kubernetes-map-type: atomic
                        storageClassName:
                          type: string
                        volumeAttributesClassName:
                          type: string
                        volumeMode:
                          type: string
                        volumeName:
                          type: string
                      type: object
                    status:
                      properties:
                        accessModes:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        allocatedResourceStatuses:
                          additionalProperties:
                            type: string
                          type: object
                          x-kubernetes-map-type: granular
                        allocatedResources:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          type: object
                        capacity:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          type: object
                        conditions:
                          items:
                            properties:
                              lastProbeTime:
                                format: date-time
                                type: string
                              lastTransitionTime:
                                format: date-time
                                type: string
                              message:
                                type: string
                              reason:
                                type: string
                              status:
                                type: string
                              type:
                                type: string
                            required:
                            - status
                            - type
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - type
                          x-kubernetes-list-type: map
                        currentVolumeAttributesClassName:
                          type: string
                        modifyVolumeStatus:
                          properties:
                            status:
                              type: string
                            targetVolumeAttributesClassName:
                              type: string
                          required:
                          - status
                          type: object
                        phase:
                          type: string
                      type: object
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              volumeMounts:
                items:
                  properties:
                    mountPath:
                      type: string
                    mountPropagation:
                      type: string
                    name:
                      type: string
                    readOnly:
                      type: boolean
                    recursiveReadOnly:
                      type: string
                    subPath:
                      type: string
                    subPathExpr:
                      type: string
                  required:
                  - mountPath
                  - name
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              volumes:
                items:
                  properties:
                    awsElasticBlockStore:
                      properties:
                        fsType:
                          type: string
                        partition:
                          format: int32
                          type: integer
                        readOnly:
                          type: boolean
                        volumeID:
                          type: string
                      required:
                      - volumeID
                      type: object
                    azureDisk:
                      properties:
                        cachingMode:
                          type: string
                        diskName:
                          type: string
                        diskURI:
                          type: string
                        fsType:
                          default: ext4
                          type: string
                        kind:
                          type: string
                        readOnly:
                          default: false
                          type: boolean
                      required:
                      - diskName
                      - diskURI
                      type: object
                    azureFile:
                      properties:
                        readOnly:
                          type: boolean
                        secretName:
                          type: string
                        shareName:
                          type: string
                      required:
                      - secretName
                      - shareName
                      type: object
                    cephfs:
                      properties:
                        monitors:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        path:
                          type: string
                        readOnly:
                          type: boolean
                        secretFile:
                          type: string
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        user:
                          type: string
                      required:
                      - monitors
                      type: object
                    cinder:
                      properties:
                        fsType:
                          type: string
                        readOnly:
                          type: boolean
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        volumeID:
                          type: string
                      required:
                      - volumeID
                      type: object
                    configMap:
                      properties:
                        defaultMode:
                          format: int32
                          type: integer
                        items:
                          items:
                            properties:
                              key:
                                type: string
                              mode:
                                format: int32
                                type: integer
                              path:
                                type: string
                            required:
                            - key
                            - path
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        name:
                          default: ""
                          type: string
                        optional:
                          type: boolean
                      type: object
                      x-kubernetes-map-type: atomic
                    csi:
                      properties:
                        driver:
                          type: string
                        fsType:
                          type: string
                        nodePublishSecretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        readOnly:
                          type: boolean
                        volumeAttributes:
                          additionalProperties:
                            type: string
                          type: object
                      required:
                      - driver
                      type: object
                    downwardAPI:
                      properties:
                        defaultMode:
                          format: int32
                          type: integer
                        items:
                          items:
                            properties:
                              fieldRef:
                                properties:
                                  apiVersion:
                                    type: string
                                  fieldPath:
                                    type: string
                                required:
                                - fieldPath
                                type: object
                                x-kubernetes-map-type: atomic
                              mode:
                                format: int32
                                type: integer
                              path:
                                type: string
                              resourceFieldRef:
                                properties:
                                  containerName:
                                    type: string
                                  divisor:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  resource:
                                    type: string
                                required:
                                - resource
                                type: object
                                x-kubernetes-map-type: atomic
                            required:
                            - path
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    emptyDir:
                      properties:
                        medium:
                          type: string
                        sizeLimit:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                      type: object
                    ephemeral:
                      properties:
                        volumeClaimTemplate:
                          properties:
                            metadata:
                              properties:
                                annotations:
                                  additionalProperties:
                                    type: string
                                  type: object
                                finalizers:
                                  items:
                                    type: string
                                  type: array
                                labels:
                                  additionalProperties:
                                    type: string
                                  type: object
                                name:
                                  type: string
                                namespace:
                                  type: string
                              type: object
                            spec:
                              properties:
                                accessModes:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                dataSource:
                                  properties:
                                    apiGroup:
                                      type: string
                                    kind:
                                      type: string
                                    name:
                                      type: string
                                  required:
                                  - kind
                                  - name
                                  type: object
                                  x-kubernetes-map-type: atomic
                                dataSourceRef:
                                  properties:
                                    apiGroup:
                                      type: string
                                    kind:
                                      type: string
                                    name:
                                      type: string
                                    namespace:
                                      type: string
                                  required:
                                  - kind
                                  - name
                                  type: object
                                resources:
                                  properties:
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                selector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                storageClassName:
                                  type: string
                                volumeAttributesClassName:
                                  type: string
                                volumeMode:
                                  type: string
                                volumeName:
                                  type: string
                              type: object
                          required:
                          - spec
                          type: object
                      type: object
                    fc:
                      properties:
                        fsType:
                          type: string
                        lun:
                          format: int32
                          type: integer
                        readOnly:
                          type: boolean
                        targetWWNs:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        wwids:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    flexVolume:
                      properties:
                        driver:
                          type: string
                        fsType:
                          type: string
                        options:
                          additionalProperties:
                            type: string
                          type: object
                        readOnly:
                          type: boolean
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                      required:
                      - driver
                      type: object
                    flocker:
                      properties:
                        datasetName:
                          type: string
                        datasetUUID:
                          type: string
                      type: object
                    gcePersistentDisk:
                      properties:
                        fsType:
                          type: string
                        partition:
                          format: int32
                          type: integer
                        pdName:
                          type: string
                        readOnly:
                          type: boolean
                      required:
                      - pdName
                      type: object
                    gitRepo:
                      properties:
                        directory:
                          type: string
                        repository:
                          type: string
                        revision:
                          type: string
                      required:
                      - repository
                      type: object
                    glusterfs:
                      properties:
                        endpoints:
                          type: string
                        path:
                          type: string
                        readOnly:
                          type: boolean
                      required:
                      - endpoints
                      - path
                      type: object
                    hostPath:
                      properties:
                        path:
                          type: string
                        type:
                          type: string
                      required:
                      - path
                      type: object
                    image:
                      properties:
                        pullPolicy:
                          type: string
                        reference:
                          type: string
                      type: object
                    iscsi:
                      properties:
                        chapAuthDiscovery:
                          type: boolean
                        chapAuthSession:
                          type: boolean
                        fsType:
                          type: string
                        initiatorName:
                          type: string
                        iqn:
                          type: string
                        iscsiInterface:
                          default: default
                          type: string
                        lun:
                          format: int32
                          type: integer
                        portals:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        readOnly:
                          type: boolean
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        targetPortal:
                          type: string
                      required:
                      - iqn
                      - lun
                      - targetPortal
                      type: object
                    name:
                      type: string
                    nfs:
                      properties:
                        path:
                          type: string
                        readOnly:
                          type: boolean
                        server:
                          type: string
                      required:
                      - path
                      - server
                      type: object
                    persistentVolumeClaim:
                      properties:
                        claimName:
                          type: string
                        readOnly:
                          type: boolean
                      required:
                      - claimName
                      type: object
                    photonPersistentDisk:
                      properties:
                        fsType:
                          type: string
                        pdID:
                          type: string
                      required:
                      - pdID
                      type: object
                    portworxVolume:
                      properties:
                        fsType:
                          type: string
                        readOnly:
                          type: boolean
                        volumeID:
                          type: string
                      required:
                      - volumeID
                      type: object
                    projected:
                      properties:
                        defaultMode:
                          format: int32
                          type: integer
                        sources:
                          items:
                            properties:
                              clusterTrustBundle:
                                properties:
                                  labelSelector:
                                    properties:
                                      matchExpressions:
                                        items:
                                          properties:
                                            key:
                                              type: string
                                            operator:
                                              type: string
                                            values:
                                              items:
                                                type: string
                                              type: array
                                              x-kubernetes-list-type: atomic
                                          required:
                                          - key
                                          - operator
                                          type: object
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      matchLabels:
                                        additionalProperties:
                                          type: string
                                        type: object
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  name:
                                    type: string
                                  optional:
                                    type: boolean
                                  path:
                                    type: string
                                  signerName:
                                    type: string
                                required:
                                - path
                                type: object
                              configMap:
                                properties:
                                  items:
                                    items:
                                      properties:
                                        key:
                                          type: string
                                        mode:
                                          format: int32
                                          type: integer
                                        path:
                                          type: string
                                      required:
                                      - key
                                      - path
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  name:
                                    default: ""
                                    type: string
                                  optional:
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                              downwardAPI:
                                properties:
                                  items:
                                    items:
                                      properties:
                                        fieldRef:
                                          properties:
                                            apiVersion:
                                              type: string
                                            fieldPath:
                                              type: string
                                          required:
                                          - fieldPath
                                          type: object
                                          x-kubernetes-map-type: atomic
                                        mode:
                                          format: int32
                                          type: integer
                                        path:
                                          type: string
                                        resourceFieldRef:
                                          properties:
                                            containerName:
                                              type: string
                                            divisor:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                              x-kubernetes-int-or-string: true
                                            resource:
                                              type: string
                                          required:
                                          - resource
                                          type: object
                                          x-kubernetes-map-type: atomic
                                      required:
                                      - path
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                type: object
                              secret:
                                properties:
                                  items:
                                    items:
                                      properties:
                                        key:
                                          type: string
                                        mode:
                                          format: int32
                                          type: integer
                                        path:
                                          type: string
                                      required:
                                      - key
                                      - path
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  name:
                                    default: ""
                                    type: string
                                  optional:
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                              serviceAccountToken:
                                properties:
                                  audience:
                                    type: string
                                  expirationSeconds:
                                    format: int64
                                    type: integer
                                  path:
                                    type: string
                                required:
                                - path
                                type: object
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    quobyte:
                      properties:
                        group:
                          type: string
                        readOnly:
                          type: boolean
                        registry:
                          type: string
                        tenant:
                          type: string
                        user:
                          type: string
                        volume:
                          type: string
                      required:
                      - registry
                      - volume
                      type: object
                    rbd:
                      properties:
                        fsType:
                          type: string
                        image:
                          type: string
                        keyring:
                          default: /etc/ceph/keyring
                          type: string
                        monitors:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        pool:
                          default: rbd
                          type: string
                        readOnly:
                          type: boolean
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        user:
                          default: admin
                          type: string
                      required:
                      - image
                      - monitors
                      type: object
                    scaleIO:
                      properties:
                        fsType:
                          default: xfs
                          type: string
                        gateway:
                          type: string
                        protectionDomain:
                          type: string
                        readOnly:
                          type: boolean
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        sslEnabled:
                          type: boolean
                        storageMode:
                          default: ThinProvisioned
                          type: string
                        storagePool:
                          type: string
                        system:
                          type: string
                        volumeName:
                          type: string
                      required:
                      - gateway
                      - secretRef
                      - system
                      type: object
                    secret:
                      properties:
                        defaultMode:
                          format: int32
                          type: integer
                        items:
                          items:
                            properties:
                              key:
                                type: string
                              mode:
                                format: int32
                                type: integer
                              path:
                                type: string
                            required:
                            - key
                            - path
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        optional:
                          type: boolean
                        secretName:
                          type: string
                      type: object
                    storageos:
                      properties:
                        fsType:
                          type: string
                        readOnly:
                          type: boolean
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        volumeName:
                          type: string
                        volumeNamespace:
                          type: string
                      type: object
                    vsphereVolume:
                      properties:
                        fsType:
                          type: string
                        storagePolicyID:
                          type: string
                        storagePolicyName:
                          type: string
                        volumePath:
                          type: string
                      required:
                      - volumePath
                      type: object
                  required:
                  - name
                  type: object
                type: array
                x-kubernetes-list-type: atomic
            required:
            - config
            type: object
            x-kubernetes-validations:
            - message: the OpenTelemetry Collector mode is set to sidecar, which does
                not support the attribute 'tolerations'
              rule: '!(self.mode == ''sidecar'' && size(self.tolerations) > 0) ||
                !has(self.tolerations)'
            - message: the OpenTelemetry Collector mode is set to sidecar, which does
                not support the attribute 'priorityClassName'
              rule: '!(self.mode == ''sidecar'' && self.priorityClassName != '''')
                || !has(self.priorityClassName)'
            - message: the OpenTelemetry Collector mode is set to sidecar, which does
                not support the attribute 'affinity'
              rule: '!(self.mode == ''sidecar'' && self.affinity != null) || !has(self.affinity)'
            - message: the OpenTelemetry Collector mode is set to sidecar, which does
                not support the attribute 'additionalContainers'
              rule: '!(self.mode == ''sidecar'' && size(self.additionalContainers)
                > 0) || !has(self.additionalContainers)'
          status:
            properties:
              image:
                type: string
              scale:
                properties:
                  replicas:
                    format: int32
                    type: integer
                  selector:
                    type: string
                  statusReplicas:
                    type: string
                type: object
              version:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      scale:
        labelSelectorPath: .status.scale.selector
        specReplicasPath: .spec.replicas
        statusReplicasPath: .status.scale.replicas
      status: {}
