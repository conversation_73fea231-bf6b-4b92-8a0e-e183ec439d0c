# This kustomization.yaml is not intended to be run by itself,
# since it depends on service name and namespace that are out of this kustomize package.
# It should be run by config/default
resources:
- bases/opentelemetry.io_opentelemetrycollectors.yaml
- bases/opentelemetry.io_instrumentations.yaml
- bases/opentelemetry.io_opampbridges.yaml
- bases/opentelemetry.io_targetallocators.yaml
# +kubebuilder:scaffold:crdkustomizeresource

# patches here are for enabling the conversion webhook for each CRD
#- patches/webhook_in_targetallocators.yaml
# +kubebuilder:scaffold:crdkustomizewebhookpatch

# patches here are for enabling the CA injection for each CRD


# the following config is for teaching kustomize how to do kustomization for CRDs.
configurations:
- kustomizeconfig.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
# - patches/webhook_in_opampbridges.yaml
patches:
- path: patches/cainjection_in_opentelemetrycollectors.yaml
- path: patches/cainjection_in_opampbridges.yaml
- path: patches/webhook_in_opentelemetrycollectors.yaml
- path: patches/cainjection_in_targetallocators.yaml
