# Adds namespace to all resources.
namespace: opentelemetry-operator-system

# Value of this field is prepended to the
# names of all resources, e.g. a deployment named
# "wordpress" becomes "alices-wordpress".
# Note that it should also match with the prefix (text before '-') of the namespace
# field above.
namePrefix: opentelemetry-operator-

# Labels to add to all resources and selectors.


  # Protect the /metrics endpoint by putting it behind auth.
  # If you want your controller-manager to expose the /metrics
  # endpoint w/o any authn/z, please comment the following line.


# the following config is for teaching kustomize how to do var substitution
vars:
- fieldref:
    fieldPath: metadata.namespace
  name: CERTIFICATE_NAMESPACE
  objref:
    group: cert-manager.io
    kind: Certificate
    name: serving-cert
    version: v1
- fieldref: {}
  name: CERTIFICATE_NAME
  objref:
    group: cert-manager.io
    kind: Certificate
    name: serving-cert
    version: v1
- fieldref:
    fieldPath: metadata.namespace
  name: SERVICE_NAMESPACE
  objref:
    kind: Service
    name: webhook-service
    version: v1
- fieldref: {}
  name: SERVICE_NAME
  objref:
    kind: Service
    name: webhook-service
    version: v1
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- ../crd
- ../rbac
- ../manager
- ../webhook
- ../certmanager
labels:
- includeSelectors: true
  pairs:
    app.kubernetes.io/name: opentelemetry-operator
patches:
- path: manager_auth_proxy_patch.yaml
- path: manager_webhook_patch.yaml
- path: webhookcainjection_patch.yaml
