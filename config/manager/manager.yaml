apiVersion: v1
kind: Namespace
metadata:
  labels:
    app.kubernetes.io/name: opentelemetry-operator
    control-plane: controller-manager
  name: system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager
  namespace: system
  labels:
    app.kubernetes.io/name: opentelemetry-operator
    control-plane: controller-manager
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: opentelemetry-operator
      control-plane: controller-manager
  replicas: 1
  template:
    metadata:
      labels:
        app.kubernetes.io/name: opentelemetry-operator
        control-plane: controller-manager
    spec:
      containers:
      - args:
        - "--metrics-addr=127.0.0.1:8080"
        - "--enable-leader-election"
        - "--zap-log-level=info"
        - "--zap-time-encoding=rfc3339nano"
        - "--enable-nginx-instrumentation=true"
        image: controller
        name: manager
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          requests:
            cpu: 100m
            memory: 64Mi
        env:
          - name: SERVICE_ACCOUNT_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.serviceAccountName
      serviceAccountName: controller-manager
      terminationGracePeriodSeconds: 10
