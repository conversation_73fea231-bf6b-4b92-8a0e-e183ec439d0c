apiVersion: operators.coreos.com/v1alpha1
kind: ClusterServiceVersion
metadata:
  annotations:
    alm-examples: '[]'
    capabilities: Deep Insights
    categories: Logging & Tracing,Monitoring
    certified: "false"
    containerImage: ghcr.io/open-telemetry/opentelemetry-operator/opentelemetry-operator
    createdAt: "2020-12-16T13:37:00+00:00"
    description: Provides the OpenTelemetry components, including the Collector
    repository: github.com/open-telemetry/opentelemetry-operator
    support: OpenTelemetry Community
  name: opentelemetry-operator.v0.0.0
  namespace: placeholder
spec:
  apiservicedefinitions: {}
  customresourcedefinitions:
    owned:
    - description: OpenTelemetryCollector is the Schema for the opentelemetrycollectors
        API.
      displayName: OpenTelemetry Collector
      kind: OpenTelemetryCollector
      name: opentelemetrycollectors.core.opentelemetry.io
      version: v1alpha1
    - description: OpenTelemetryCollector is the Schema for the opentelemetrycollectors
        API.
      displayName: OpenTelemetry Collector
      kind: OpenTelemetryCollector
      name: opentelemetrycollectors.opentelemetry.io
      resources:
      - kind: ConfigMaps
        name: ""
        version: v1
      - kind: DaemonSets
        name: ""
        version: apps/v1
      - kind: Deployment
        name: ""
        version: apps/v1
      - kind: Pod
        name: ""
        version: v1
      - kind: Service
        name: ""
        version: v1
      - kind: StatefulSets
        name: ""
        version: apps/v1
      specDescriptors:
      - description: ObservabilitySpec defines how telemetry data gets handled.
        displayName: Observability
        path: observability
      - description: Metrics defines the metrics configuration for operands.
        displayName: Metrics Config
        path: observability.metrics
      - description: EnableMetrics specifies if ServiceMonitor or PodMonitor(for sidecar
          mode) should be created for the service managed by the OpenTelemetry Operator.
        displayName: Create ServiceMonitors for OpenTelemetry Collector
        path: observability.metrics.enableMetrics
      - description: ObservabilitySpec defines how telemetry data gets handled.
        displayName: Observability
        path: targetAllocator.observability
      - description: Metrics defines the metrics configuration for operands.
        displayName: Metrics Config
        path: targetAllocator.observability.metrics
      - description: EnableMetrics specifies if ServiceMonitor or PodMonitor(for sidecar
          mode) should be created for the service managed by the OpenTelemetry Operator.
        displayName: Create ServiceMonitors for OpenTelemetry Collector
        path: targetAllocator.observability.metrics.enableMetrics
      version: v1beta1
    - description: Instrumentation is the spec for OpenTelemetry instrumentation.
      displayName: OpenTelemetry Instrumentation
      kind: Instrumentation
      name: instrumentations.opentelemetry.io
      resources:
      - kind: Pod
        name: ""
        version: v1
      version: v1alpha1
    - description: OpAMPBridge is the Schema for the opampbridges API.
      displayName: OpAMP Bridge
      kind: OpAMPBridge
      name: opampbridges.opentelemetry.io
      resources:
      - kind: ConfigMaps
        name: ""
        version: v1
      - kind: Deployment
        name: ""
        version: apps/v1
      - kind: Pod
        name: ""
        version: v1
      - kind: Service
        name: ""
        version: v1
      version: v1alpha1
    - description: OpenTelemetryCollector is the Schema for the opentelemetrycollectors
        API.
      displayName: OpenTelemetry Collector
      kind: OpenTelemetryCollector
      name: opentelemetrycollectors.opentelemetry.io
      resources:
      - kind: ConfigMaps
        name: ""
        version: v1
      - kind: DaemonSets
        name: ""
        version: apps/v1
      - kind: Deployment
        name: ""
        version: apps/v1
      - kind: Ingress
        name: ""
        version: networking/v1
      - kind: Pod
        name: ""
        version: v1
      - kind: Service
        name: ""
        version: v1
      - kind: StatefulSets
        name: ""
        version: apps/v1
      specDescriptors:
      - description: ObservabilitySpec defines how telemetry data gets handled.
        displayName: Observability
        path: observability
      - description: Metrics defines the metrics configuration for operands.
        displayName: Metrics Config
        path: observability.metrics
      - description: EnableMetrics specifies if ServiceMonitor or PodMonitor(for sidecar
          mode) should be created for the service managed by the OpenTelemetry Operator.
        displayName: Create ServiceMonitors for OpenTelemetry Collector
        path: observability.metrics.enableMetrics
      - description: ObservabilitySpec defines how telemetry data gets handled.
        displayName: Observability
        path: targetAllocator.observability
      - description: Metrics defines the metrics configuration for operands.
        displayName: Metrics Config
        path: targetAllocator.observability.metrics
      - description: EnableMetrics specifies if ServiceMonitor or PodMonitor(for sidecar
          mode) should be created for the service managed by the OpenTelemetry Operator.
        displayName: Create ServiceMonitors for OpenTelemetry Collector
        path: targetAllocator.observability.metrics.enableMetrics
      version: v1alpha1
    - description: TargetAllocator is the Schema for the targetallocators API.
      displayName: Target Allocator
      kind: TargetAllocator
      name: targetallocators.opentelemetry.io
      resources:
      - kind: ConfigMaps
        name: ""
        version: v1
      - kind: Deployment
        name: ""
        version: apps/v1
      - kind: Pod
        name: ""
        version: v1
      - kind: PodDisruptionBudget
        name: ""
        version: policy/v1
      - kind: Service
        name: ""
        version: v1
      - kind: ServiceAccount
        name: ""
        version: v1
      specDescriptors:
      - description: ObservabilitySpec defines how telemetry data gets handled.
        displayName: Observability
        path: observability
      - description: Metrics defines the metrics configuration for operands.
        displayName: Metrics Config
        path: observability.metrics
      - description: Metrics defines the metrics configuration for operands.
        displayName: Metrics Config
        path: observability.metrics
      - description: EnableMetrics specifies if ServiceMonitor or PodMonitor(for sidecar
          mode) should be created for the service managed by the OpenTelemetry Operator.
        displayName: Create ServiceMonitors for OpenTelemetry Collector
        path: observability.metrics.enableMetrics
      - description: EnableMetrics specifies if ServiceMonitor or PodMonitor(for sidecar
          mode) should be created for the service managed by the OpenTelemetry Operator.
        displayName: Create ServiceMonitors for OpenTelemetry Collector
        path: observability.metrics.enableMetrics
      version: v1alpha1
  description: |-
    OpenTelemetry is a collection of tools, APIs, and SDKs. You use it to instrument, generate, collect, and export telemetry data (metrics, logs, and traces) for analysis in order to understand your software's performance and behavior.

    ### Operator features

    * **Sidecar injection** - annotate your pods and let the operator inject a sidecar.
    * **Managed upgrades** - updating the operator will automatically update your OpenTelemetry collectors.
    * **Deployment modes** - your collector can be deployed as sidecar, daemon set, or regular deployment.
    * **Service port management** - the operator detects which ports need to be exposed based on the provided configuration.
  displayName: Community OpenTelemetry Operator
  icon:
  - base64data: PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHJvbGU9ImltZyIgdmlld0JveD0iLTEyLjcwIC0xMi43MCAxMDI0LjQwIDEwMjQuNDAiPjxzdHlsZT5zdmcge2VuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMTAwMCAxMDAwfTwvc3R5bGU+PHBhdGggZmlsbD0iI2Y1YTgwMCIgZD0iTTUyOC43IDU0NS45Yy00MiA0Mi00MiAxMTAuMSAwIDE1Mi4xczExMC4xIDQyIDE1Mi4xIDAgNDItMTEwLjEgMC0xNTIuMS0xMTAuMS00Mi0xNTIuMSAwem0xMTMuNyAxMTMuOGMtMjAuOCAyMC44LTU0LjUgMjAuOC03NS4zIDAtMjAuOC0yMC44LTIwLjgtNTQuNSAwLTc1LjMgMjAuOC0yMC44IDU0LjUtMjAuOCA3NS4zIDAgMjAuOCAyMC43IDIwLjggNTQuNSAwIDc1LjN6bTM2LjYtNjQzbC02NS45IDY1LjljLTEyLjkgMTIuOS0xMi45IDM0LjEgMCA0N2wyNTcuMyAyNTcuM2MxMi45IDEyLjkgMzQuMSAxMi45IDQ3IDBsNjUuOS02NS45YzEyLjktMTIuOSAxMi45LTM0LjEgMC00N0w3MjUuOSAxNi43Yy0xMi45LTEyLjktMzQtMTIuOS00Ni45IDB6TTIxNy4zIDg1OC44YzExLjctMTEuNyAxMS43LTMwLjggMC00Mi41bC0zMy41LTMzLjVjLTExLjctMTEuNy0zMC44LTExLjctNDIuNSAwTDcyLjEgODUybC0uMS4xLTE5LTE5Yy0xMC41LTEwLjUtMjcuNi0xMC41LTM4IDAtMTAuNSAxMC41LTEwLjUgMjcuNiAwIDM4bDExNCAxMTRjMTAuNSAxMC41IDI3LjYgMTAuNSAzOCAwczEwLjUtMjcuNiAwLTM4bC0xOS0xOSAuMS0uMSA2OS4yLTY5LjJ6Ii8+PHBhdGggZmlsbD0iIzQyNWNjNyIgZD0iTTU2NS45IDIwNS45TDQxOS41IDM1Mi4zYy0xMyAxMy0xMyAzNC40IDAgNDcuNGw5MC40IDkwLjRjNjMuOS00NiAxNTMuNS00MC4zIDIxMSAxNy4ybDczLjItNzMuMmMxMy0xMyAxMy0zNC40IDAtNDcuNEw2MTMuMyAyMDUuOWMtMTMtMTMuMS0zNC40LTEzLjEtNDcuNCAwem0tOTQgMzIyLjNsLTUzLjQtNTMuNGMtMTIuNS0xMi41LTMzLTEyLjUtNDUuNSAwTDE4NC43IDY2My4yYy0xMi41IDEyLjUtMTIuNSAzMyAwIDQ1LjVsMTA2LjcgMTA2LjdjMTIuNSAxMi41IDMzIDEyLjUgNDUuNSAwTDQ1OCA2OTQuMWMtMjUuNi01Mi45LTIxLTExNi44IDEzLjktMTY1Ljl6Ii8+PC9zdmc+
    mediatype: image/svg+xml
  install:
    spec:
      deployments: null
    strategy: ""
  installModes:
  - supported: false
    type: OwnNamespace
  - supported: false
    type: SingleNamespace
  - supported: false
    type: MultiNamespace
  - supported: true
    type: AllNamespaces
  keywords:
  - opentelemetry
  - tracing
  - logging
  - metrics
  - monitoring
  - troubleshooting
  links:
  - name: OpenTelemetry Operator
    url: https://github.com/open-telemetry/opentelemetry-operator
  maintainers:
  - email: <EMAIL>
    name: Juraci Paixão Kröhling
  maturity: alpha
  minKubeVersion: 1.25.0
  provider:
    name: OpenTelemetry Community
  version: 0.0.0
