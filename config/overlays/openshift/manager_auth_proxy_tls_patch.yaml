apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager
  namespace: system
spec:
  template:
    spec:
      containers:
      - name: manager # without this line, kustomize reorders the containers, making kube-rbac-proxy the default container
      - name: kube-rbac-proxy
        args:
        - "--secure-listen-address=0.0.0.0:8443"
        - "--upstream=http://127.0.0.1:8080/"
        - "--logtostderr=true"
        - "--v=0"
        - "--tls-cert-file=/var/run/tls/server/tls.crt"
        - "--tls-private-key-file=/var/run/tls/server/tls.key"
        - "--tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_128_GCM_SHA256,TLS_RSA_WITH_AES_256_GCM_SHA384,TLS_RSA_WITH_AES_128_CBC_SHA256"
        - "--tls-min-version=VersionTLS12"
        volumeMounts:
        - mountPath: /var/run/tls/server
          name: opentelemetry-operator-metrics-cert
      volumes:
      - name: opentelemetry-operator-metrics-cert
        secret:
          defaultMode: 420
          # secret generated by the 'service.beta.openshift.io/serving-cert-secret-name' annotation on the metrics-service
          secretName: opentelemetry-operator-metrics
