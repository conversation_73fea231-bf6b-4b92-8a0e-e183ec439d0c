# permissions for end users to edit targetallocators.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/instance: targetallocator-editor-role
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: opentelemetry-operator
    app.kubernetes.io/part-of: opentelemetry-operator
    app.kubernetes.io/managed-by: kustomize
  name: targetallocator-editor-role
rules:
- apiGroups:
  - opentelemetry.io
  resources:
  - targetallocators
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - opentelemetry.io
  resources:
  - targetallocators/status
  verbs:
  - get
