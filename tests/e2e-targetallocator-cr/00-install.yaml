apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: (join('-', ['cr-targetallocator', $namespace]))
rules:
  - apiGroups:
      - ""
    resources:
      - pods
      - namespaces
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - monitoring.coreos.com
    resources:
      - servicemonitors
    verbs:
      - get
      - list
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: (join('-', ['cr-targetallocator', $namespace]))
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: (join('-', ['cr-targetallocator', $namespace]))
subjects:
  - kind: ServiceAccount
    name: cr-targetallocator
    namespace: ($namespace)
---
apiVersion: opentelemetry.io/v1alpha1
kind: TargetAllocator
metadata:
  name: cr
spec:
  scrapeConfigs:
    - job_name: prometheus
      static_configs:
      - targets: [ "localhost:9090" ]
