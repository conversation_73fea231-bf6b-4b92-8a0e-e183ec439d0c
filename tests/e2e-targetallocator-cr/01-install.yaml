
---
apiVersion: opentelemetry.io/v1alpha1
kind: TargetAllocator
metadata:
  name: cr
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: ingress-ready
                operator: In
                values:
                  - "true"
  env:
    - name: TEST_ENV
      value: test
  podSecurityContext:
    fsGroup: 3000
    runAsGroup: 3000
    runAsUser: 1000
  prometheusCR:
    enabled: true
  scrapeConfigs: []
  securityContext:
    capabilities:
      add:
        - NET_BIND_SERVICE
      drop:
        - ALL
    privileged: false
    runAsGroup: 1000
    runAsUser: 1000
  serviceAccount: cr-targetallocator
  volumeMounts:
    - mountPath: /usr/share/testvolume
      name: testvolume
  volumes:
    - name: testvolume
      emptyDir: {}
