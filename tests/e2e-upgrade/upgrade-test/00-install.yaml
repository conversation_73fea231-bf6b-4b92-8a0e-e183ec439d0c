apiVersion: opentelemetry.io/v1alpha1
kind: OpenTelemetryCollector
metadata:
  name: simplest
  annotations:
    operatorVersion: "v0.86.0"
spec:
  replicas: 1
  config: |
    receivers:
      jaeger:
        protocols:
          grpc:
      otlp:
        protocols:
          grpc:
          http:
    processors:

    exporters:
      debug:

    service:
      pipelines:
        traces:
          receivers: [jaeger,otlp]
          exporters: [debug]
