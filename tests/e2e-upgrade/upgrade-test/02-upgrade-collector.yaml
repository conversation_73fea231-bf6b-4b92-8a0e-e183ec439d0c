apiVersion: opentelemetry.io/v1alpha1
kind: OpenTelemetryCollector
metadata:
  name: simplest
  annotations:
    operatorVersion: "latest"
spec:
  replicas: 2
  config: |
    receivers:
      jaeger:
        protocols:
          grpc:
      otlp:
        protocols:
          grpc:
          http:
    processors:

    exporters:
      debug:

    service:
      pipelines:
        traces:
          receivers: [jaeger,otlp]
          exporters: [debug]
