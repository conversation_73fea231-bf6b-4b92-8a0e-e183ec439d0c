# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  creationTimestamp: null
  name: upgrade-test
spec:
  steps:
  - name: step-00
    try:
    - apply:
        file: 00-install.yaml
    - assert:
        file: 00-assert.yaml
  - name: step-01
    try:
    - script:
        timeout: 5m
        content: cd ../../../ && make deploy VERSION=e2e
  - name: step-02
    try:
    - apply:
        file: 02-upgrade-collector.yaml
    - assert:
        file: 02-assert.yaml
